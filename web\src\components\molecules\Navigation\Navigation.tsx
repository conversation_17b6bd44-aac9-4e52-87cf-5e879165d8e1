import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../design-system/theme/ThemeProvider';
import { Typography } from '../../atoms';

interface NavigationProps {
  children: ReactNode;
}

interface NavItemProps {
  isActive?: boolean;
  theme: any; // Using `any` for now due to complex theme typing, will refine if needed
}

const StyledNav = styled.nav`
  display: flex;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.radius.md};
  padding: ${(props) => props.theme.spacing.sm};
  box-shadow: ${(props) => props.theme.shadows.sm};
`;

const NavList = styled.ul`
  list-style: none;
  display: flex;
  gap: ${(props) => props.theme.spacing.md};
  width: 100%;
  justify-content: space-around;
`;

const NavItemContainer = styled.li<NavItemProps>`
  flex: 1;
  text-align: center;
  padding: ${(props) => props.theme.spacing.xs};
  border-radius: ${(props) => props.theme.radius.sm};
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${(props) =>
    props.isActive ? props.theme.colors.primary : 'transparent'};
  color: ${(props) =>
    props.isActive ? props.theme.colors.background : props.theme.colors.text};

  &:hover {
    background-color: ${(props) =>
      props.isActive ? props.theme.colors.primary : props.theme.colors.surface};
    opacity: 0.8;
  }
`;

interface NavItemComponentProps {
  children: ReactNode;
  isActive?: boolean;
  onClick?: () => void;
}

export const NavItem = ({ children, isActive, onClick }: NavItemComponentProps) => {
  const { theme } = useTheme();
  return (
    <NavItemContainer isActive={isActive} onClick={onClick}>
      <Typography variant="body" color={isActive ? theme.colors.background : theme.colors.text}>
        {children}
      </Typography>
    </NavItemContainer>
  );
};

export const Navigation = ({ children, ...props }: NavigationProps) => {
  const { theme } = useTheme();

  return (
    <StyledNav {...props}>
      <NavList>{children}</NavList>
    </StyledNav>
  );
}; 