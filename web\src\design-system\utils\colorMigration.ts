// 颜色迁移映射 - 用于将旧的颜色引用映射到新的设计系统
export const colorMigrationMap = {
  // 旧的颜色引用 -> 新的颜色引用
  'theme.colors.primary': 'theme.colors.primary[500]',
  'theme.colors.secondary': 'theme.colors.secondary[500]',
  'theme.colors.tertiary': 'theme.colors.primary[300]',
  'theme.colors.quaternary': 'theme.colors.secondary[300]',
  'theme.colors.background': 'theme.colors.background.primary',
  'theme.colors.surface': 'theme.colors.surface.primary',
  'theme.colors.text': 'theme.colors.text.primary',
  'theme.colors.textSecondary': 'theme.colors.text.secondary',
  'theme.colors.separator': 'theme.colors.border.primary',
  'theme.colors.red': 'theme.colors.error[500]',
  'theme.colors.green': 'theme.colors.success[500]',
  'theme.colors.blue': 'theme.colors.info[500]',
  'theme.colors.yellow': 'theme.colors.warning[500]',
  'theme.colors.orange': 'theme.colors.secondary[500]',
  'theme.colors.purple': 'theme.colors.accent',
  'theme.colors.gray': 'theme.colors.gray[500]',
};

// 尺寸迁移映射
export const sizeMigrationMap = {
  'small': 'sm',
  'medium': 'md', 
  'large': 'lg',
};

// 自动迁移函数（仅用于开发时参考）
export const migrateColorReference = (oldRef: string): string => {
  return colorMigrationMap[oldRef as keyof typeof colorMigrationMap] || oldRef;
};

export const migrateSizeReference = (oldSize: string): string => {
  return sizeMigrationMap[oldSize as keyof typeof sizeMigrationMap] || oldSize;
};
