const express = require('express');
const router = express.Router();

let personas = [];
let currentPersonaId = null;
let idCounter = 1;

// 創建人格
router.post('/', (req, res) => {
  const { name, tags, avatar, prompt } = req.body;
  if (!name) return res.status(400).json({ message: '名稱必填' });
  const persona = { id: idCounter++, name, tags: tags || [], avatar: avatar || '', prompt: prompt || '' };
  personas.push(persona);
  res.json(persona);
});

// 查詢所有人格
router.get('/', (req, res) => {
  res.json(personas);
});

// 編輯人格
router.put('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const persona = personas.find(p => p.id === id);
  if (!persona) return res.status(404).json({ message: '人格不存在' });
  const { name, tags, avatar, prompt } = req.body;
  if (name) persona.name = name;
  if (tags) persona.tags = tags;
  if (avatar) persona.avatar = avatar;
  if (prompt) persona.prompt = prompt;
  res.json(persona);
});

// 刪除人格
router.delete('/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const idx = personas.findIndex(p => p.id === id);
  if (idx === -1) return res.status(404).json({ message: '人格不存在' });
  personas.splice(idx, 1);
  if (currentPersonaId === id) currentPersonaId = null;
  res.json({ message: '已刪除' });
});

// 切換當前人格
router.post('/switch/:id', (req, res) => {
  const id = parseInt(req.params.id);
  if (!personas.find(p => p.id === id)) return res.status(404).json({ message: '人格不存在' });
  currentPersonaId = id;
  res.json({ currentPersonaId });
});

// 查詢當前人格
router.get('/current', (req, res) => {
  const persona = personas.find(p => p.id === currentPersonaId);
  res.json(persona || null);
});

module.exports = router; 