const express = require('express');
const router = express.Router();
const { runQuery, getQuery, allQuery } = require('../models/db');
const { auth } = require('./authMiddleware');
const { v4: uuidv4 } = require('uuid');

// 創建人格
router.post('/', auth, async (req, res) => {
  const { name, description, traits, avatar, prompt } = req.body;
  if (!name) return res.status(400).json({ message: '名稱必填' });

  try {
    const id = uuidv4();
    const traitsJson = JSON.stringify(traits || []);

    await runQuery(
      'INSERT INTO personas (id, name, description, traits, avatar, prompt, user_account) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [id, name, description || '', traitsJson, avatar || '', prompt || '', req.user.account]
    );

    const persona = {
      id,
      name,
      description: description || '',
      traits: traits || [],
      avatar: avatar || '',
      prompt: prompt || ''
    };

    res.json(persona);
  } catch (err) {
    console.error('Error creating persona:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// 查詢所有人格
router.get('/', auth, async (req, res) => {
  try {
    const personas = await allQuery(
      'SELECT * FROM personas WHERE user_account = ? ORDER BY created_at DESC',
      [req.user.account]
    );

    // 解析traits JSON
    const parsedPersonas = personas.map(persona => ({
      ...persona,
      traits: JSON.parse(persona.traits || '[]')
    }));

    res.json(parsedPersonas);
  } catch (err) {
    console.error('Error fetching personas:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// 編輯人格
router.put('/:id', auth, async (req, res) => {
  const { id } = req.params;
  const { name, description, traits, avatar, prompt } = req.body;

  try {
    // 检查人格是否存在且属于当前用户
    const existingPersona = await getQuery(
      'SELECT * FROM personas WHERE id = ? AND user_account = ?',
      [id, req.user.account]
    );

    if (!existingPersona) {
      return res.status(404).json({ message: '人格不存在' });
    }

    const traitsJson = JSON.stringify(traits || []);

    await runQuery(
      'UPDATE personas SET name = ?, description = ?, traits = ?, avatar = ?, prompt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_account = ?',
      [name || existingPersona.name, description || existingPersona.description, traitsJson, avatar || existingPersona.avatar, prompt || existingPersona.prompt, id, req.user.account]
    );

    const updatedPersona = {
      id,
      name: name || existingPersona.name,
      description: description || existingPersona.description,
      traits: traits || JSON.parse(existingPersona.traits || '[]'),
      avatar: avatar || existingPersona.avatar,
      prompt: prompt || existingPersona.prompt
    };

    res.json(updatedPersona);
  } catch (err) {
    console.error('Error updating persona:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// 刪除人格
router.delete('/:id', auth, async (req, res) => {
  const { id } = req.params;

  try {
    // 检查人格是否存在且属于当前用户
    const existingPersona = await getQuery(
      'SELECT * FROM personas WHERE id = ? AND user_account = ?',
      [id, req.user.account]
    );

    if (!existingPersona) {
      return res.status(404).json({ message: '人格不存在' });
    }

    await runQuery(
      'DELETE FROM personas WHERE id = ? AND user_account = ?',
      [id, req.user.account]
    );

    res.json({ message: '已刪除' });
  } catch (err) {
    console.error('Error deleting persona:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// 切換當前人格
router.post('/switch/:id', auth, async (req, res) => {
  const { id } = req.params;

  try {
    // 检查人格是否存在且属于当前用户
    const existingPersona = await getQuery(
      'SELECT * FROM personas WHERE id = ? AND user_account = ?',
      [id, req.user.account]
    );

    if (!existingPersona) {
      return res.status(404).json({ message: '人格不存在' });
    }

    // 先将所有人格设为非当前
    await runQuery(
      'UPDATE personas SET is_current = 0 WHERE user_account = ?',
      [req.user.account]
    );

    // 设置指定人格为当前
    await runQuery(
      'UPDATE personas SET is_current = 1 WHERE id = ? AND user_account = ?',
      [id, req.user.account]
    );

    res.json({ currentPersonaId: id });
  } catch (err) {
    console.error('Error switching persona:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// 查詢當前人格
router.get('/current', auth, async (req, res) => {
  try {
    const currentPersona = await getQuery(
      'SELECT * FROM personas WHERE user_account = ? AND is_current = 1',
      [req.user.account]
    );

    if (currentPersona) {
      currentPersona.traits = JSON.parse(currentPersona.traits || '[]');
    }

    res.json(currentPersona || null);
  } catch (err) {
    console.error('Error fetching current persona:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

module.exports = router; 