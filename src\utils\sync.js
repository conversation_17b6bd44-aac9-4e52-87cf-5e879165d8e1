import axios from 'axios';
import CryptoJS from 'crypto-js';

const SYNC_KEY = 'your-strong-key'; // 請根據實際情況安全存儲

export function encryptData(data) {
  const str = JSON.stringify(data);
  return CryptoJS.AES.encrypt(str, SYNC_KEY).toString();
}

export function decryptData(encrypted) {
  const bytes = CryptoJS.AES.decrypt(encrypted, SYNC_KEY);
  const str = bytes.toString(CryptoJS.enc.Utf8);
  return JSON.parse(str);
}

export async function uploadSyncData(userId, data) {
  const encryptedData = encryptData(data);
  await axios.post('/api/sync/upload', { userId, encryptedData });
}

export async function downloadSyncData(userId) {
  const res = await axios.get('/api/sync/download', { params: { userId } });
  if (res.data.encryptedData) {
    return decryptData(res.data.encryptedData);
  }
  return null;
} 