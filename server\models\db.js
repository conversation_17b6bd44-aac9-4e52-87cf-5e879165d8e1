const sqlite3 = require('sqlite3').verbose();
const DB_PATH = './ai_configs.db'; // 数据库文件路径

let db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('Error connecting to database:', err.message);
  } else {
    console.log('Connected to the SQLite database.');
    // 创建 ai_configs 表
    db.run(`CREATE TABLE IF NOT EXISTS ai_configs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      provider TEXT NOT NULL,
      model_id TEXT NOT NULL,
      api_url TEXT,
      api_key_env_var TEXT NOT NULL,
      is_active INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`, (err) => {
      if (err) {
        console.error('Error creating ai_configs table:', err.message);
      } else {
        console.log('ai_configs table created or already exists.');
      }
    });

    // 创建 users 表
    db.run(`CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      account TEXT NOT NULL UNIQUE,
      password TEXT NOT NULL,
      role TEXT DEFAULT 'user',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`, (err) => {
      if (err) {
        console.error('Error creating users table:', err.message);
      } else {
        console.log('users table created or already exists.');
      }
    });

    // 创建 personas 表
    db.run(`CREATE TABLE IF NOT EXISTS personas (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      traits TEXT,
      avatar TEXT,
      prompt TEXT,
      user_account TEXT NOT NULL,
      is_current INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_account) REFERENCES users (account)
    )`, (err) => {
      if (err) {
        console.error('Error creating personas table:', err.message);
      } else {
        console.log('personas table created or already exists.');
      }
    });
  }
});

/**
 * @function runQuery
 * @description 执行一个 SQL 查询，不返回结果（例如：INSERT, UPDATE, DELETE）
 * @param {string} sql - SQL 语句
 * @param {Array} params - SQL 参数
 * @returns {Promise<Object>}
 */
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

/**
 * @function getQuery
 * @description 执行一个 SQL 查询，返回单行结果
 * @param {string} sql - SQL 语句
 * @param {Array} params - SQL 参数
 * @returns {Promise<Object>}
 */
function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

/**
 * @function allQuery
 * @description 执行一个 SQL 查询，返回所有结果
 * @param {string} sql - SQL 语句
 * @param {Array} params - SQL 参数
 * @returns {Promise<Array>}
 */
function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

module.exports = {
  db,
  runQuery,
  getQuery,
  allQuery
};