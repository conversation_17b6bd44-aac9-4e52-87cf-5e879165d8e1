import { css } from 'styled-components';

export const breakpoints = {
  mobile: '375px',
  tablet: '768px',
  desktop: '1024px',
};

export const media = {
  mobile: (...args: Parameters<typeof css>) => css`
    @media (max-width: ${breakpoints.mobile}) {
      ${css(...args)}
    }
  `,
  tablet: (...args: Parameters<typeof css>) => css`
    @media (max-width: ${breakpoints.tablet}) {
      ${css(...args)}
    }
  `,
  desktop: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${breakpoints.desktop}) {
      ${css(...args)}
    }
  `,
};

