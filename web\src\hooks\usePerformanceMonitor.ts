import { useEffect } from 'react';

export const usePerformanceMonitor = () => {
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      console.warn('PerformanceObserver API not supported in this environment.');
      return;
    }

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'largest-contentful-paint') {
          const lcpEntry = entry as PerformanceEntry & { renderTime: number; loadTime: number; size: number; url: string; };
          console.log('LCP:', lcpEntry.renderTime || lcpEntry.loadTime);
        }
        if (entry.entryType === 'first-input') {
          const fidEntry = entry as PerformanceEntry & { processingStart: number; startTime: number; };
          console.log('FID:', fidEntry.processingStart - fidEntry.startTime);
        }
        // You can add more metrics here, e.g., CLS, FCP
      });
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input'] });

    return () => {
      observer.disconnect();
    };
  }, []);
}; 