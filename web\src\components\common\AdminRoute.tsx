import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { LoadingSpinner } from '../animations/LoadingSpinner';
import styled from 'styled-components';

interface AdminRouteProps {
  children: React.ReactNode;
}

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
`;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
  padding: 20px;
`;

const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  const { user, loading, isAuthenticated, isAdmin } = useAuth();

  if (loading) {
    return (
      <LoadingContainer>
        <LoadingSpinner />
      </LoadingContainer>
    );
  }

  if (!isAuthenticated()) {
    return <Navigate to="/auth/login" replace />;
  }

  if (!isAdmin()) {
    return (
      <ErrorContainer>
        <h2>🚫 權限不足</h2>
        <p>您需要管理員權限才能訪問此頁面</p>
        <p>當前用戶：{user?.account} ({user?.role})</p>
      </ErrorContainer>
    );
  }

  return <>{children}</>;
};

export default AdminRoute;
