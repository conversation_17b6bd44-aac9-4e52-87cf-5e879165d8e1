{"name": "web", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.7", "axios": "^0.27.2", "date-fns": "^4.1.0", "framer-motion": "^11.3.2", "http-proxy-middleware": "^3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "recharts": "^2.15.3", "styled-components": "^6.1.11", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/axios": "^0.9.36", "@types/react-router-dom": "^5.3.3", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0"}}