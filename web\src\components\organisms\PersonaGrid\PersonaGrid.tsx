import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { media } from '../../../design-system/utils/styleUtils';
import { useTheme } from 'design-system/theme/ThemeProvider';
import { motion } from 'framer-motion';

interface PersonaGridProps {
  children: ReactNode;
}

const StyledPersonaGrid = styled.div`
  display: grid;
  gap: ${(props) => props.theme.spacing.md};
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));

  ${media.tablet`
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  `}

  ${media.mobile`
    grid-template-columns: 1fr;
  `}
`;

export const PersonaGrid = ({ children, ...props }: PersonaGridProps) => {
  const { theme } = useTheme();
  return (
    <StyledPersonaGrid {...props}>
      {children}
    </StyledPersonaGrid>
  );
}; 