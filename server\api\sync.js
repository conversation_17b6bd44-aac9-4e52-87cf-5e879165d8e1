const express = require('express');
const router = express.Router();

// 模擬用戶加密數據存儲（實際應用可用數據庫）
let userSyncData = {};

// 上傳加密數據
// POST /api/sync/upload  body: { userId, encryptedData }
router.post('/upload', (req, res) => {
  const { userId, encryptedData } = req.body;
  if (!userId || !encryptedData) return res.status(400).json({ message: '參數缺失' });
  userSyncData[userId] = encryptedData;
  res.json({ message: '同步數據已保存' });
});

// 下載加密數據
// GET /api/sync/download?userId=xxx
router.get('/download', (req, res) => {
  const { userId } = req.query;
  if (!userId) return res.status(400).json({ message: '參數缺失' });
  res.json({ encryptedData: userSyncData[userId] || null });
});

module.exports = router; 