import React, { useState } from 'react';
import axios from 'axios';
import { AuthLayout } from '../../layouts/AuthLayout';
import { Button, Input, Typography } from '../../components/atoms';
import { Card, FormField } from '../../components/molecules';
import { LoadingSpinner } from '../../components/animations/LoadingSpinner';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../design-system/theme/ThemeProvider';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.theme.spacing.md};
  margin-top: ${(props) => props.theme.spacing.lg};
`;

const PageTitle = styled(Typography)`
  text-align: center;
`;

const ErrorMessage = styled(Typography)`
  margin-top: ${(props) => props.theme.spacing.sm};
  text-align: center;
`;

const Login: React.FC = () => {
  const { theme } = useTheme();
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    try {
      const res = await axios.post('/api/user/login', { account, password });
      localStorage.setItem('token', (res.data as { token: string }).token);
      window.location.href = '/';
    } catch (err: any) {
      setError(err.response?.data?.message || '登錄失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <Card>
        <PageTitle variant="title1">歡迎回來</PageTitle>
        <Form onSubmit={handleSubmit}>
          <FormField label="賬號">
            <Input
              type="text"
              placeholder="請輸入賬號"
              value={account}
              onChange={(e) => setAccount(e.target.value)}
              required
            />
          </FormField>
          <FormField label="密碼">
            <Input
              type="password"
              placeholder="請輸入密碼"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </FormField>
          <Button
            variant="primary"
            size="lg"
            loading={loading}
            disabled={loading}
            type="submit"
          >
            {loading ? <LoadingSpinner /> : '登錄'}
          </Button>
        </Form>
        {error && <ErrorMessage variant="body" color={theme.colors.red}>{error}</ErrorMessage>}
      </Card>
    </AuthLayout>
  );
};

export default Login; 