import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { AuthLayout } from '../../layouts/AuthLayout';
import { Button, Input, Typography } from '../../components/atoms';
import { Card, FormField } from '../../components/molecules';
import { LoadingSpinner } from '../../components/animations/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../design-system/theme/ThemeProvider';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.theme.spacing.md};
  margin-top: ${(props) => props.theme.spacing.lg};
`;

const PageTitle = styled(Typography)`
  text-align: center;
`;

const ErrorMessage = styled(Typography)`
  margin-top: ${(props) => props.theme.spacing.sm};
  text-align: center;
`;

const RegisterSection = styled.div`
  margin-top: ${(props) => props.theme.spacing.lg};
  text-align: center;
  padding-top: ${(props) => props.theme.spacing.md};
  border-top: 1px solid ${(props) => props.theme.colors.separator};
`;

const RegisterText = styled(Typography)`
  margin-bottom: ${(props) => props.theme.spacing.sm};
`;

const Login: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    try {
      const res = await axios.post('/api/user/login', { account, password });
      await login((res.data as { token: string }).token);
      navigate('/');
    } catch (err: any) {
      setError(err.response?.data?.message || '登錄失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <Card>
        <PageTitle variant="title1">歡迎回來</PageTitle>
        <Form onSubmit={handleSubmit}>
          <FormField label="賬號">
            <Input
              type="text"
              placeholder="請輸入賬號"
              value={account}
              onChange={(e) => setAccount(e.target.value)}
              required
            />
          </FormField>
          <FormField label="密碼">
            <Input
              type="password"
              placeholder="請輸入密碼"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </FormField>
          <Button
            variant="primary"
            size="lg"
            loading={loading}
            disabled={loading}
            type="submit"
          >
            {loading ? <LoadingSpinner /> : '登錄'}
          </Button>
        </Form>
        {error && <ErrorMessage variant="body" color={theme.colors.red}>{error}</ErrorMessage>}

        <RegisterSection>
          <RegisterText variant="body" color="textSecondary">
            還沒有帳號？
          </RegisterText>
          <Button
            variant="ghost"
            size="md"
            onClick={() => navigate('/auth/register')}
            disabled={loading}
          >
            立即註冊
          </Button>
        </RegisterSection>
      </Card>
    </AuthLayout>
  );
};

export default Login; 