import React, { useEffect, useState } from 'react';
import axios from 'axios';
import styled from 'styled-components';
import { format } from 'date-fns';

// Import new components
import StatsCard from '../../components/organisms/StatsCard/StatsCard';
import ConversationTimeline from '../../components/organisms/ConversationTimeline/ConversationTimeline';
import GrowthChart from '../../components/organisms/GrowthChart/GrowthChart';
import { SearchBar } from '../../components/molecules/SearchBar/SearchBar'; // Assuming SearchBar is in molecules
import { Select } from '../../components/atoms/Select/Select'; // Select is now in atoms
import { Typography } from '../../components/atoms';

// Assuming a DashboardLayout or PageTransition might be needed, adjust as per design system
// import DashboardLayout from '../../layouts/DashboardLayout'; 
// import { PageTransition } from '../../components/animations';

interface Conversation {
  id: string;
  date: Date;
  persona: string;
  summary: string;
  mood: 'positive' | 'neutral' | 'negative';
  duration: number;
}

interface Stats {
  totalConversations: number;
  activePersonas: number;
  growthIndex: number;
  dailyActivity: { name: string; value: number }[];
}

const ArchivePage: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>('week');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [stats, setStats] = useState<Stats>({ totalConversations: 0, activePersonas: 0, growthIndex: 0, dailyActivity: [] });
  const [searchTerm, setSearchTerm] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        const chatRes = await axios.get<any>('/api/archive/chats');
        // Map messages to ConversationTimeline's TimelineItem format
        const formattedConversations: Conversation[] = chatRes.data.flatMap((chat: any) =>
          chat.messages.map((m: any, idx: number) => ({
            id: `${chat.id}-${idx}`,
            date: new Date(m.timestamp),
            persona: chat.personas.map((pId: number) => `Persona ${pId}`).join('、'), // Placeholder for persona name
            summary: m.content.substring(0, 50) + '...', // Short summary
            mood: 'neutral', // Placeholder for mood
            duration: Math.floor(Math.random() * 30) + 5, // Random duration for demo
          }))
        );
        setConversations(formattedConversations);

        const reportRes = await axios.get<Stats>('/api/archive/report');
        setStats(reportRes.data);
      } catch (error) {
        console.error('Error fetching archive data:', error);
      }
    };
    fetchData();
  }, [timeRange]); // Re-fetch data when timeRange changes

  const filteredConversations = conversations.filter(conv =>
    conv.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.persona.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <PageContainer>
      <Header>
        <Typography variant="largeTitle">成長檔案</Typography>
        <Controls>
          <SearchBar
            placeholder="搜尋對話記錄..."
            onSearch={setSearchTerm}
          />
          <Select
            value={timeRange}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setTimeRange(e.target.value)}
            options={[
              { value: 'week', label: '近一週' },
              { value: 'month', label: '近一月' },
              { value: 'year', label: '近一年' },
            ]}
          />
        </Controls>
      </Header>

      <StatsSection>
        <StatsCard
          title="總對話次數"
          value={stats.totalConversations}
          unit="次"
          trend="up"
          trendValue={12}
        />
        <StatsCard
          title="活躍人格"
          value={stats.activePersonas}
          unit="個"
        />
        <StatsCard
          title="成長指數"
          value={stats.growthIndex}
          unit="分"
          trend="up"
          trendValue={8}
        />
      </StatsSection>

      <ChartSection>
        <GrowthChart data={stats.dailyActivity} />
      </ChartSection>

      <TimelineSection>
        <Typography variant="title2">對話記錄</Typography>
        <ConversationTimeline items={filteredConversations} />
      </TimelineSection>
    </PageContainer>
  );
};

const PageContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.xl};
  max-width: 1200px;
  margin: auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Controls = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.md};
  align-items: center;
`;

const StatsSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const ChartSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const TimelineSection = styled.div`
  /* Add any specific styling for timeline section if needed */
`;

export default ArchivePage; 