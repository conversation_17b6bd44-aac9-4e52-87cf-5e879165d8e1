import React, { useEffect, useState } from 'react';
import axios from 'axios';
import PromptTest from './PromptTest';
import {
  Button,
  Input,
  Typography,
} from '../../components/atoms';
import {
  Card,
  Modal,
  FormField,
} from '../../components/molecules';
import {
  PersonaCard,
  PersonaGrid,
} from '../../components/organisms';
import styled from 'styled-components';
import { Persona } from '../../types/persona';

const PersonaManager: React.FC = () => {
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [current, setCurrent] = useState<string | null>(null);
  const [form, setForm] = useState<Partial<Persona>>({});
  const [editId, setEditId] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const fetchPersonas = async () => {
    const res = await axios.get('/api/persona');
    setPersonas((res.data as Persona[]).map((p) => ({ ...p, traits: p.traits || [] })));

    const cur = await axios.get('/api/persona/current');
    setCurrent((cur.data as { id?: string })?.id || null);
  };

  useEffect(() => { fetchPersonas(); }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    try {
      const dataToSend = { ...form, traits: form.traits || [] };
      if (editId) {
        await axios.put(`/api/persona/${editId}`, dataToSend);
      } else {
        await axios.post('/api/persona', dataToSend);
      }
      setForm({});
      setEditId(null);
      setIsModalOpen(false);
      fetchPersonas();
    } catch (err: any) {
      setError(err.response?.data?.message || '操作失敗');
    }
  };

  const handleEdit = (p: Persona) => {
    setForm(p);
    setEditId(p.id);
    setIsModalOpen(true);
  };

  const handleDelete = async (id: string) => {
    await axios.delete(`/api/persona/${id}`);
    fetchPersonas();
  };

  const handleSwitch = async (id: string) => {
    await axios.post(`/api/persona/switch/${id}`);
    setCurrent(id);
  };

  const handleCreateNew = () => {
    setForm({});
    setEditId(null);
    setIsModalOpen(true);
  };

  return (
    <PageContainer>
      <Header>
        <Typography variant="title1">人格管理</Typography>
        <Button onClick={handleCreateNew}>創建新人格</Button>
      </Header>
      {error && <ErrorText>{error}</ErrorText>}
      <PersonaGrid>
        {personas.map((p) => (
          <PersonaCard
            key={p.id}
            persona={p}
            isCurrent={current === p.id}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSwitch={handleSwitch}
          />
        ))}
      </PersonaGrid>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setForm({});
          setEditId(null);
        }}
        title={editId ? '編輯人格' : '創建新人格'}
      >
        <FormContainer onSubmit={handleSubmit}>
          <FormField label="名稱" htmlFor="persona-name">
            <Input
              id="persona-name"
              name="name"
              placeholder="人格名稱"
              value={form.name || ''}
              onChange={handleChange}
              required
            />
          </FormField>
          <FormField label="描述" htmlFor="persona-description">
            <Input
              as="textarea"
              id="persona-description"
              name="description"
              placeholder="人格描述"
              value={form.description || ''}
              onChange={handleChange}
              rows={3}
            />
          </FormField>
          <FormField label="特徵（逗號分隔）" htmlFor="persona-traits">
            <Input
              id="persona-traits"
              name="traits"
              placeholder="特徵（逗號分隔）"
              value={form.traits?.join(',') || ''}
              onChange={(e) =>
                setForm({ ...form, traits: e.target.value.split(',') })
              }
            />
          </FormField>
          <FormField label="頭像URL" htmlFor="persona-avatar">
            <Input
              id="persona-avatar"
              name="avatar"
              placeholder="頭像URL"
              value={form.avatar || ''}
              onChange={handleChange}
            />
          </FormField>
          <FormField label="Prompt" htmlFor="persona-prompt">
            <Input
              as="textarea"
              id="persona-prompt"
              name="prompt"
              placeholder="Prompt內容"
              value={form.prompt || ''}
              onChange={handleChange}
              rows={5}
            />
          </FormField>
          <Button type="submit" style={{ marginTop: '20px' }}>
            {editId ? '保存編輯' : '創建人格'}
          </Button>
        </FormContainer>
      </Modal>
    </PageContainer>
  );
};

const PageContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.xl};
  max-width: 1200px;
  margin: auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const ErrorText = styled(Typography).attrs({ variant: 'body' })`
  color: ${({ theme }) => theme.colors.red[500]};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const FormContainer = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

export default PersonaManager;