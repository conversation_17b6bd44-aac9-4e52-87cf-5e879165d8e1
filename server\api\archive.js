const express = require('express');
const router = express.Router();

// 假設與 chat.js 共用 chats 數據
const chatApi = require('./chat');
let chats = require('./chat').chats || [];

// 高光記錄：{ chatId, messageIdx }
let highlights = [];

// 查詢所有對話記錄
router.get('/chats', (req, res) => {
  res.json(chats);
});

// 標記/取消高光
router.post('/highlight', (req, res) => {
  const { chatId, messageIdx } = req.body;
  if (typeof chatId !== 'number' || typeof messageIdx !== 'number') return res.status(400).json({ message: '參數錯誤' });
  const idx = highlights.findIndex(h => h.chatId === chatId && h.messageIdx === messageIdx);
  if (idx === -1) {
    highlights.push({ chatId, messageIdx });
    res.json({ message: '已標記高光' });
  } else {
    highlights.splice(idx, 1);
    res.json({ message: '已取消高光' });
  }
});

// 查詢高光
router.get('/highlights', (req, res) => {
  res.json(highlights);
});

// 生成簡單成長報告（統計消息數、對話數）
router.get('/report', (req, res) => {
  const totalChats = chats.length;
  const totalMessages = chats.reduce((sum, c) => sum + c.messages.length, 0);
  res.json({ totalChats, totalMessages });
});

// 簡單情緒分析（正負面詞彙統計）
const positiveWords = ['好', '棒', '開心', '快樂', '讚'];
const negativeWords = ['難過', '生氣', '糟', '壞', '失望'];
router.get('/emotion', (req, res) => {
  let pos = 0, neg = 0;
  chats.forEach(c => c.messages.forEach(m => {
    positiveWords.forEach(w => { if (m.content.includes(w)) pos++; });
    negativeWords.forEach(w => { if (m.content.includes(w)) neg++; });
  }));
  res.json({ positive: pos, negative: neg });
});

module.exports = router; 