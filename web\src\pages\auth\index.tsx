import React, { useState } from 'react';
import Login from './Login';
import Register from './Register';

const AuthPage: React.FC = () => {
  const [mode, setMode] = useState<'login' | 'register'>('login');
  return (
    <div>
      <div style={{ textAlign: 'center', margin: 16 }}>
        <button onClick={() => setMode('login')}>登錄</button>
        <button onClick={() => setMode('register')}>註冊</button>
      </div>
      {mode === 'login' ? <Login /> : <Register />}
    </div>
  );
};

export default AuthPage; 