import React from 'react';
import styled from 'styled-components';

interface NavigationProps {
  children: React.ReactNode;
  className?: string; // Allow styled-components to pass className
}

const Navigation: React.FC<NavigationProps> = ({ children, className }) => {
  return <NavContainer className={className}>{children}</NavContainer>;
};

const NavContainer = styled.nav`
  /* Styles will be primarily applied by StyledNavigation in AppLayout */
`;

export default Navigation; 