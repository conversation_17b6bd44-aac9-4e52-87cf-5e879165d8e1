import React from 'react';
import styled, { DefaultTheme } from 'styled-components';
import { motion } from 'framer-motion';
import { Button, Typography } from '../../atoms'; // Assuming Button and Typography are from atoms
import { Card } from '../../molecules'; // Assuming Card is from molecules
import { useTheme } from 'design-system/theme/ThemeProvider';
import { Persona } from '../../../types/persona'; // Import shared Persona type
import LazyImage from '../../common/LazyImage'; // Import LazyImage

interface PersonaCardProps {
  persona: Persona;
  onEdit: (p: Persona) => void;
  onDelete: (id: string) => void;
  isCurrent: boolean;
  onSwitch: (id: string) => void;
}

const Avatar = styled(LazyImage)` // Placeholder for Avatar, assuming it's an image
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: ${(props) => props.theme.spacing.md};
`;

const TagContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${(props) => props.theme.spacing.xs};
  margin-top: ${(props) => props.theme.spacing.sm};
  margin-bottom: ${(props) => props.theme.spacing.md};
`;

const Tag = styled.span` // Placeholder for Tag
  background-color: ${(props) => props.theme.colors.tertiary};
  color: ${(props) => props.theme.colors.background};
  padding: ${(props) => props.theme.spacing.xxs} ${(props) => props.theme.spacing.xs};
  border-radius: ${(props) => props.theme.radius.sm};
  font-size: ${(props) => props.theme.typography.caption1.fontSize};
`;

const ActionButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${(props) => props.theme.spacing.sm};
  margin-top: ${(props) => props.theme.spacing.md};
`;

export const PersonaCard = ({
  persona,
  onEdit,
  onDelete,
  onSwitch,
  isCurrent,
  ...props
}: PersonaCardProps) => {
  const { theme } = useTheme();

  return (
    <Card whileHover={{ scale: 1.02 }} {...props}>
      <Avatar src={persona.avatar} alt={persona.name} />
      <Typography variant="title3">{persona.name}</Typography>
      <Typography variant="body" color={theme.colors.textSecondary}>
        {persona.description}
      </Typography>
      <TagContainer>
        {persona.traits.map((trait: string) => (
          <Tag key={trait}>{trait}</Tag>
        ))}
      </TagContainer>
      <ActionButtons>
        <Button variant="ghost" onClick={() => onEdit(persona)}>
          編輯
        </Button>
        <Button variant="ghost" onClick={() => onDelete(persona.id)}>
          刪除
        </Button>
        <Button variant="ghost" onClick={() => onSwitch(persona.id)} disabled={isCurrent}>
          {isCurrent ? '當前' : '切換'}
        </Button>
      </ActionButtons>
    </Card>
  );
}; 