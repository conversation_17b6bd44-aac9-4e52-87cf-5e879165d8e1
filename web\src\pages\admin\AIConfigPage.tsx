import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import axios from 'axios';
import { Button } from '../../components/atoms/Button/Button';
import { Input } from '../../components/atoms/Input/Input';
import { Typography } from '../../components/atoms/Typography/Typography';
import { Card } from '../../components/molecules/Card/Card';
import { FormField } from '../../components/molecules/FormField/FormField';
import { Modal } from '../../components/molecules/Modal/Modal';
import { LoadingSpinner } from '../../components/animations/LoadingSpinner';

// 样式组件
const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
  min-height: 100vh;
  background-color: ${({ theme }) => theme.colors.background};

  @media (max-width: 768px) {
    padding: 20px 16px;
  }

  @media (max-width: 480px) {
    padding: 16px 12px;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.separator};

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
  }

  @media (max-width: 480px) {
    margin-bottom: 20px;
    padding-bottom: 12px;
  }
`;

const ConfigGrid = styled.div`
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  margin-bottom: 32px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  @media (max-width: 480px) {
    gap: 12px;
  }
`;

const ConfigCard = styled(Card)`
  padding: 24px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:focus-within {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }

  @media (max-width: 768px) {
    padding: 20px;
  }

  @media (max-width: 480px) {
    padding: 16px;
  }

  /* 移动端触摸优化 */
  @media (hover: none) and (pointer: coarse) {
    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
`;

const ConfigHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const ConfigInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const StatusBadge = styled.span<{ active: boolean }>`
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: ${({ theme, active }) =>
    active ? theme.colors.green : theme.colors.gray};
  color: ${({ theme }) => theme.colors.background};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;

  @media (max-width: 768px) {
    flex-wrap: wrap;
    gap: 8px;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 8px;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
`;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: ${({ theme }) => theme.colors.red};
  text-align: center;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const NotificationContainer = styled.div<{ show: boolean; type: NotificationState['type'] }>`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  padding: 16px 20px;
  border-radius: ${({ theme }) => theme.radius.md};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  transform: translateX(${({ show }) => show ? '0' : '400px'});
  opacity: ${({ show }) => show ? '1' : '0'};
  transition: all 0.3s ease;
  max-width: 400px;

  ${({ theme, type }) => {
    switch (type) {
      case 'success':
        return `
          background-color: ${theme.colors.green};
          color: ${theme.colors.background};
        `;
      case 'error':
        return `
          background-color: ${theme.colors.red};
          color: ${theme.colors.background};
        `;
      default:
        return `
          background-color: ${theme.colors.primary};
          color: ${theme.colors.background};
        `;
    }
  }}

  @media (max-width: 768px) {
    top: 16px;
    right: 16px;
    left: 16px;
    max-width: none;
    transform: translateY(${({ show }) => show ? '0' : '-100px'});
  }

  @media (max-width: 480px) {
    top: 12px;
    right: 12px;
    left: 12px;
    padding: 12px 16px;
  }
`;

const NotificationContent = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const NotificationIcon = styled.span`
  font-size: 20px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px;
  margin-left: auto;
  border-radius: 4px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;

const ConfirmDialogContent = styled.div`
  text-align: center;
  padding: 20px 0;
`;

const ConfirmDialogButtons = styled.div`
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
`;

const BatchOperationBar = styled.div<{ show: boolean }>`
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.radius.lg};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 100;
  transition: all 0.3s ease;
  opacity: ${({ show }) => show ? 1 : 0};
  visibility: ${({ show }) => show ? 'visible' : 'hidden'};
  transform: translateX(-50%) translateY(${({ show }) => show ? '0' : '20px'});
  border: 1px solid ${({ theme }) => theme.colors.separator};
  max-width: calc(100vw - 40px);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    bottom: 16px;
    left: 16px;
    right: 16px;
    transform: none;
    max-width: none;
  }

  @media (max-width: 480px) {
    bottom: 12px;
    left: 12px;
    right: 12px;
    padding: 12px;
    gap: 8px;
  }
`;

const BatchInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${({ theme }) => theme.colors.text};
`;

const BatchActions = styled.div`
  display: flex;
  gap: 8px;

  @media (max-width: 768px) {
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 6px;
  }
`;

const SelectAllContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.radius.md};
  border: 1px solid ${({ theme }) => theme.colors.separator};
`;

const CheckboxContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StyledCheckbox = styled.input`
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: ${({ theme }) => theme.colors.primary};

  @media (hover: none) and (pointer: coarse) {
    width: 24px;
    height: 24px;
  }
`;

interface AIConfig {
  id: string;
  name: string;
  provider: string;
  model_id: string;
  api_url: string;
  api_key_env_var: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FormErrors {
  name?: string;
  provider?: string;
  model_id?: string;
  api_url?: string;
  api_key_env_var?: string;
}

interface NotificationState {
  show: boolean;
  type: 'success' | 'error' | 'info';
  message: string;
}

interface ConfirmDialogState {
  show: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
}

const AIConfigPage: React.FC = () => {
  const [configs, setConfigs] = useState<AIConfig[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newConfig, setNewConfig] = useState<Partial<AIConfig>>({
    name: '',
    provider: '',
    model_id: '',
    api_url: '',
    api_key_env_var: '',
    is_active: true,
  });
  const [editingConfig, setEditingConfig] = useState<AIConfig | null>(null);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [isSelectMode, setIsSelectMode] = useState<boolean>(false);
  const [batchOperating, setBatchOperating] = useState<boolean>(false);
  const [notification, setNotification] = useState<NotificationState>({
    show: false,
    type: 'info',
    message: ''
  });
  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    show: false,
    title: '',
    message: '',
    onConfirm: () => {},
    confirmText: '确认',
    cancelText: '取消'
  });

  /**
   * 表单验证函数
   */
  const validateForm = useCallback((config: Partial<AIConfig>): FormErrors => {
    const errors: FormErrors = {};

    if (!config.name?.trim()) {
      errors.name = '配置名称不能为空';
    } else if (config.name.length < 2) {
      errors.name = '配置名称至少需要2个字符';
    }

    if (!config.provider?.trim()) {
      errors.provider = '提供商不能为空';
    }

    if (!config.model_id?.trim()) {
      errors.model_id = '模型ID不能为空';
    }

    if (!config.api_url?.trim()) {
      errors.api_url = 'API URL不能为空';
    } else {
      try {
        new URL(config.api_url);
      } catch {
        errors.api_url = '请输入有效的URL格式';
      }
    }

    if (!config.api_key_env_var?.trim()) {
      errors.api_key_env_var = '环境变量名不能为空';
    } else if (!/^[A-Z_][A-Z0-9_]*$/.test(config.api_key_env_var)) {
      errors.api_key_env_var = '环境变量名格式不正确（应为大写字母和下划线）';
    }

    return errors;
  }, []);

  /**
   * 显示通知消息
   */
  const showNotification = useCallback((type: NotificationState['type'], message: string) => {
    setNotification({ show: true, type, message });
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 5000);
  }, []);

  /**
   * 获取所有AI配置
   */
  const fetchAIConfigs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get<AIConfig[]>('/api/configs/ai');
      setConfigs(response.data);
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to fetch AI configurations.';
      setError(errorMessage);
      showNotification('error', errorMessage);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理新增配置表单提交
   */
  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 验证表单
    const errors = validateForm(newConfig);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      showNotification('error', '请修正表单中的错误');
      return;
    }

    setIsSubmitting(true);
    try {
      await axios.post('/api/configs/ai', newConfig);
      setShowAddForm(false);
      setNewConfig({
        name: '',
        provider: '',
        model_id: '',
        api_url: '',
        api_key_env_var: '',
        is_active: true,
      });
      setFormErrors({});
      showNotification('success', '配置添加成功！');
      fetchAIConfigs(); // Refresh the list
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to add AI configuration.';
      setError(errorMessage);
      showNotification('error', errorMessage);
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * 处理编辑配置表单提交
   */
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingConfig) return;

    // 验证表单
    const errors = validateForm(editingConfig);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      showNotification('error', '请修正表单中的错误');
      return;
    }

    setIsSubmitting(true);
    try {
      await axios.put(`/api/configs/ai/${editingConfig.id}`, editingConfig);
      setEditingConfig(null);
      setFormErrors({});
      showNotification('success', '配置更新成功！');
      fetchAIConfigs(); // Refresh the list
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to update AI configuration.';
      setError(errorMessage);
      showNotification('error', errorMessage);
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * 显示删除确认对话框
   */
  const showDeleteConfirm = (id: string, name: string) => {
    setConfirmDialog({
      show: true,
      title: '确认删除',
      message: `确定要删除配置 "${name}" 吗？此操作不可撤销。`,
      onConfirm: () => handleDeleteConfirmed(id, name),
      confirmText: '删除',
      cancelText: '取消'
    });
  };

  /**
   * 处理删除配置
   */
  const handleDeleteConfirmed = async (id: string, name: string) => {
    setConfirmDialog(prev => ({ ...prev, show: false }));
    setDeletingId(id);
    try {
      await axios.delete(`/api/configs/ai/${id}`);
      showNotification('success', `配置 "${name}" 删除成功！`);
      fetchAIConfigs(); // Refresh the list
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to delete AI configuration.';
      setError(errorMessage);
      showNotification('error', errorMessage);
      console.error(err);
    } finally {
      setDeletingId(null);
    }
  };

  /**
   * 批量操作处理函数
   */
  const handleSelectAll = useCallback(() => {
    if (selectedIds.size === configs.length) {
      setSelectedIds(new Set());
    } else {
      setSelectedIds(new Set(configs.map(config => config.id)));
    }
  }, [selectedIds.size, configs]);

  const handleSelectItem = useCallback((id: string) => {
    const newSelected = new Set(selectedIds);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedIds(newSelected);
  }, [selectedIds]);

  const handleBatchDelete = useCallback(() => {
    const selectedConfigs = configs.filter(config => selectedIds.has(config.id));
    const names = selectedConfigs.map(config => config.name).join('、');

    setConfirmDialog({
      show: true,
      title: '批量删除确认',
      message: `确定要删除以下 ${selectedIds.size} 个配置吗？\n${names}\n\n此操作不可撤销。`,
      onConfirm: handleBatchDeleteConfirmed,
      confirmText: '删除',
      cancelText: '取消'
    });
  }, [configs, selectedIds]);

  const handleBatchDeleteConfirmed = useCallback(async () => {
    setConfirmDialog(prev => ({ ...prev, show: false }));
    setBatchOperating(true);

    try {
      const deletePromises = Array.from(selectedIds).map(id =>
        axios.delete(`/api/configs/ai/${id}`)
      );

      await Promise.all(deletePromises);

      showNotification('success', `成功删除 ${selectedIds.size} 个配置！`);
      setSelectedIds(new Set());
      setIsSelectMode(false);
      fetchAIConfigs();
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to delete configurations.';
      showNotification('error', errorMessage);
      console.error(err);
    } finally {
      setBatchOperating(false);
    }
  }, [selectedIds]);

  const handleBatchToggleStatus = useCallback(async (enable: boolean) => {
    setBatchOperating(true);

    try {
      const selectedConfigs = configs.filter(config => selectedIds.has(config.id));
      const updatePromises = selectedConfigs.map(config =>
        axios.put(`/api/configs/ai/${config.id}`, {
          ...config,
          is_active: enable
        })
      );

      await Promise.all(updatePromises);

      showNotification('success', `成功${enable ? '启用' : '禁用'} ${selectedIds.size} 个配置！`);
      setSelectedIds(new Set());
      setIsSelectMode(false);
      fetchAIConfigs();
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Failed to update configurations.';
      showNotification('error', errorMessage);
      console.error(err);
    } finally {
      setBatchOperating(false);
    }
  }, [configs, selectedIds]);

  /**
   * 键盘导航处理
   */
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // ESC 键关闭模态框
    if (e.key === 'Escape') {
      if (showAddForm) {
        setShowAddForm(false);
        setFormErrors({});
      } else if (editingConfig) {
        setEditingConfig(null);
        setFormErrors({});
      } else if (confirmDialog.show) {
        setConfirmDialog(prev => ({ ...prev, show: false }));
      }
    }
  }, [showAddForm, editingConfig, confirmDialog.show]);

  useEffect(() => {
    fetchAIConfigs();
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  if (loading) {
    return (
      <PageContainer>
        <LoadingContainer>
          <LoadingSpinner />
        </LoadingContainer>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer>
        <ErrorContainer>
          <Typography variant="title2" color="red">
            ⚠️ 加载失败
          </Typography>
          <Typography variant="body" style={{ marginTop: '8px' }}>
            {error}
          </Typography>
          <Button
            variant="primary"
            onClick={fetchAIConfigs}
            style={{ marginTop: '16px' }}
          >
            重试
          </Button>
        </ErrorContainer>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      {/* 通知组件 */}
      <NotificationContainer show={notification.show} type={notification.type}>
        <NotificationContent>
          <NotificationIcon>
            {notification.type === 'success' ? '✅' :
             notification.type === 'error' ? '❌' : 'ℹ️'}
          </NotificationIcon>
          <Typography variant="body">{notification.message}</Typography>
          <CloseButton onClick={() => setNotification(prev => ({ ...prev, show: false }))}>
            ✕
          </CloseButton>
        </NotificationContent>
      </NotificationContainer>

      <Header>
        <div>
          <Typography variant="largeTitle">🤖 AI 配置管理</Typography>
          <Typography variant="body" color="textSecondary" style={{ marginTop: '8px' }}>
            管理和配置 AI 模型接口设置
          </Typography>
        </div>
        <ActionButtons>
          {configs.length > 0 && (
            <Button
              variant="ghost"
              onClick={() => {
                setIsSelectMode(!isSelectMode);
                setSelectedIds(new Set());
              }}
              disabled={isSubmitting}
            >
              {isSelectMode ? '取消选择' : '批量操作'}
            </Button>
          )}
          <Button
            variant="primary"
            onClick={() => {
              setShowAddForm(true);
              setEditingConfig(null);
              setFormErrors({});
            }}
            disabled={isSubmitting}
          >
            ➕ 添加新配置
          </Button>
        </ActionButtons>
      </Header>

      {configs.length === 0 ? (
        <EmptyState>
          <Typography variant="title2">
            🔧 暂无配置
          </Typography>
          <Typography variant="body" color="textSecondary">
            点击上方按钮添加您的第一个 AI 配置
          </Typography>
        </EmptyState>
      ) : (
        <>
          {isSelectMode && (
            <SelectAllContainer>
              <CheckboxContainer>
                <StyledCheckbox
                  type="checkbox"
                  checked={selectedIds.size === configs.length && configs.length > 0}
                  onChange={handleSelectAll}
                  aria-label="全选/取消全选"
                />
                <Typography variant="body">
                  {selectedIds.size === configs.length && configs.length > 0 ? '取消全选' : '全选'}
                </Typography>
              </CheckboxContainer>
              <Typography variant="caption1" color="textSecondary">
                已选择 {selectedIds.size} / {configs.length} 项
              </Typography>
            </SelectAllContainer>
          )}

          <ConfigGrid role="grid" aria-label="AI配置列表">
            {configs.map((config, index) => (
            <ConfigCard
              key={config.id}
              role="gridcell"
              tabIndex={0}
              aria-label={`配置: ${config.name}, ${config.provider} ${config.model_id}, ${config.is_active ? '已启用' : '已禁用'}`}
              aria-rowindex={index + 1}
            >
              {isSelectMode && (
                <CheckboxContainer style={{ position: 'absolute', top: '16px', left: '16px' }}>
                  <StyledCheckbox
                    type="checkbox"
                    checked={selectedIds.has(config.id)}
                    onChange={() => handleSelectItem(config.id)}
                    aria-label={`选择配置 ${config.name}`}
                  />
                </CheckboxContainer>
              )}
              <ConfigHeader style={{ marginLeft: isSelectMode ? '40px' : '0' }}>
                <ConfigInfo>
                  <Typography variant="title3">
                    <span id={`config-name-${config.id}`}>{config.name}</span>
                  </Typography>
                  <Typography variant="body" color="textSecondary">
                    {config.provider} • {config.model_id}
                  </Typography>
                </ConfigInfo>
                <StatusBadge
                  active={config.is_active}
                  role="status"
                  aria-label={config.is_active ? '配置已启用' : '配置已禁用'}
                >
                  {config.is_active ? '✅ 启用' : '❌ 禁用'}
                </StatusBadge>
              </ConfigHeader>

              <ConfigInfo>
                <Typography variant="caption1" color="textSecondary">
                  API URL: {config.api_url}
                </Typography>
                <Typography variant="caption1" color="textSecondary">
                  环境变量: {config.api_key_env_var}
                </Typography>
                <Typography variant="caption1" color="textSecondary">
                  创建时间: {new Date(config.created_at).toLocaleDateString()}
                </Typography>
              </ConfigInfo>

              <ActionButtons role="group" aria-label={`${config.name} 操作按钮`}>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    setEditingConfig(config);
                    setShowAddForm(false);
                    setFormErrors({});
                  }}
                  disabled={isSubmitting}
                  aria-label={`编辑配置 ${config.name}`}
                  aria-describedby={`config-name-${config.id}`}
                >
                  ✏️ 编辑
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => showDeleteConfirm(config.id, config.name)}
                  disabled={isSubmitting || deletingId === config.id}
                  loading={deletingId === config.id}
                  aria-label={`删除配置 ${config.name}`}
                  aria-describedby={`config-name-${config.id}`}
                >
                  {deletingId === config.id ? '删除中...' : '🗑️ 删除'}
                </Button>
              </ActionButtons>
            </ConfigCard>
          ))}
          </ConfigGrid>
        </>
      )}

      {/* 添加配置模态框 */}
      <Modal
        isOpen={showAddForm}
        onClose={() => {
          setShowAddForm(false);
          setFormErrors({});
        }}
        title="添加新的 AI 配置"
        size="md"
      >
        <form
          onSubmit={handleAddSubmit}
          aria-label="添加新的AI配置表单"
          noValidate
        >
          <FormContainer role="group" aria-label="配置信息">
            <FormField label="配置名称" required error={formErrors.name} htmlFor="add-config-name">
              <Input
                id="add-config-name"
                type="text"
                value={newConfig.name || ''}
                onChange={(e) => {
                  setNewConfig({ ...newConfig, name: e.target.value });
                  if (formErrors.name) {
                    setFormErrors({ ...formErrors, name: undefined });
                  }
                }}
                placeholder="例如：GPT-4 配置"
                error={!!formErrors.name}
                aria-invalid={!!formErrors.name}
                aria-describedby={formErrors.name ? "add-config-name-error" : undefined}
                required
              />
            </FormField>

            <FormField label="提供商" required error={formErrors.provider} htmlFor="add-config-provider">
              <Input
                id="add-config-provider"
                type="text"
                value={newConfig.provider || ''}
                onChange={(e) => {
                  setNewConfig({ ...newConfig, provider: e.target.value });
                  if (formErrors.provider) {
                    setFormErrors({ ...formErrors, provider: undefined });
                  }
                }}
                placeholder="例如：OpenAI"
                error={!!formErrors.provider}
                aria-invalid={!!formErrors.provider}
                aria-describedby={formErrors.provider ? "add-config-provider-error" : undefined}
                required
              />
            </FormField>

            <FormField label="模型 ID" required error={formErrors.model_id} htmlFor="add-config-model">
              <Input
                id="add-config-model"
                type="text"
                value={newConfig.model_id || ''}
                onChange={(e) => {
                  setNewConfig({ ...newConfig, model_id: e.target.value });
                  if (formErrors.model_id) {
                    setFormErrors({ ...formErrors, model_id: undefined });
                  }
                }}
                placeholder="例如：gpt-4"
                error={!!formErrors.model_id}
                aria-invalid={!!formErrors.model_id}
                aria-describedby={formErrors.model_id ? "add-config-model-error" : undefined}
                required
              />
            </FormField>

            <FormField label="API URL" required error={formErrors.api_url}>
              <Input
                type="url"
                value={newConfig.api_url || ''}
                onChange={(e) => {
                  setNewConfig({ ...newConfig, api_url: e.target.value });
                  if (formErrors.api_url) {
                    setFormErrors({ ...formErrors, api_url: undefined });
                  }
                }}
                placeholder="https://api.openai.com/v1"
                error={!!formErrors.api_url}
                required
              />
            </FormField>

            <FormField label="API Key 环境变量" required error={formErrors.api_key_env_var}>
              <Input
                type="text"
                value={newConfig.api_key_env_var || ''}
                onChange={(e) => {
                  setNewConfig({ ...newConfig, api_key_env_var: e.target.value });
                  if (formErrors.api_key_env_var) {
                    setFormErrors({ ...formErrors, api_key_env_var: undefined });
                  }
                }}
                placeholder="OPENAI_API_KEY"
                error={!!formErrors.api_key_env_var}
                required
              />
            </FormField>

            <FormField label="启用状态">
              <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <input
                  type="checkbox"
                  checked={newConfig.is_active || false}
                  onChange={(e) => setNewConfig({ ...newConfig, is_active: e.target.checked })}
                />
                <Typography variant="body">启用此配置</Typography>
              </label>
            </FormField>
          </FormContainer>

          <ActionButtons>
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                setShowAddForm(false);
                setFormErrors({});
              }}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {isSubmitting ? '添加中...' : '添加配置'}
            </Button>
          </ActionButtons>
        </form>
      </Modal>

      {/* 编辑配置模态框 */}
      <Modal
        isOpen={!!editingConfig}
        onClose={() => {
          setEditingConfig(null);
          setFormErrors({});
        }}
        title="编辑 AI 配置"
        size="md"
      >
        {editingConfig && (
          <form onSubmit={handleEditSubmit}>
            <FormContainer>
              <FormField label="配置名称" required error={formErrors.name}>
                <Input
                  type="text"
                  value={editingConfig.name}
                  onChange={(e) => {
                    setEditingConfig({ ...editingConfig, name: e.target.value });
                    if (formErrors.name) {
                      setFormErrors({ ...formErrors, name: undefined });
                    }
                  }}
                  error={!!formErrors.name}
                  required
                />
              </FormField>

              <FormField label="提供商" required error={formErrors.provider}>
                <Input
                  type="text"
                  value={editingConfig.provider}
                  onChange={(e) => {
                    setEditingConfig({ ...editingConfig, provider: e.target.value });
                    if (formErrors.provider) {
                      setFormErrors({ ...formErrors, provider: undefined });
                    }
                  }}
                  error={!!formErrors.provider}
                  required
                />
              </FormField>

              <FormField label="模型 ID" required error={formErrors.model_id}>
                <Input
                  type="text"
                  value={editingConfig.model_id}
                  onChange={(e) => {
                    setEditingConfig({ ...editingConfig, model_id: e.target.value });
                    if (formErrors.model_id) {
                      setFormErrors({ ...formErrors, model_id: undefined });
                    }
                  }}
                  error={!!formErrors.model_id}
                  required
                />
              </FormField>

              <FormField label="API URL" required error={formErrors.api_url}>
                <Input
                  type="url"
                  value={editingConfig.api_url}
                  onChange={(e) => {
                    setEditingConfig({ ...editingConfig, api_url: e.target.value });
                    if (formErrors.api_url) {
                      setFormErrors({ ...formErrors, api_url: undefined });
                    }
                  }}
                  error={!!formErrors.api_url}
                  required
                />
              </FormField>

              <FormField label="API Key 环境变量" required error={formErrors.api_key_env_var}>
                <Input
                  type="text"
                  value={editingConfig.api_key_env_var}
                  onChange={(e) => {
                    setEditingConfig({ ...editingConfig, api_key_env_var: e.target.value });
                    if (formErrors.api_key_env_var) {
                      setFormErrors({ ...formErrors, api_key_env_var: undefined });
                    }
                  }}
                  error={!!formErrors.api_key_env_var}
                  required
                />
              </FormField>

              <FormField label="启用状态">
                <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <input
                    type="checkbox"
                    checked={editingConfig.is_active}
                    onChange={(e) => setEditingConfig({ ...editingConfig, is_active: e.target.checked })}
                  />
                  <Typography variant="body">启用此配置</Typography>
                </label>
              </FormField>
            </FormContainer>

            <ActionButtons>
              <Button
                type="button"
                variant="ghost"
                onClick={() => {
                  setEditingConfig(null);
                  setFormErrors({});
                }}
                disabled={isSubmitting}
              >
                取消
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? '更新中...' : '更新配置'}
              </Button>
            </ActionButtons>
          </form>
        )}
      </Modal>

      {/* 确认对话框 */}
      <Modal
        isOpen={confirmDialog.show}
        onClose={() => setConfirmDialog(prev => ({ ...prev, show: false }))}
        title={confirmDialog.title}
        size="sm"
      >
        <ConfirmDialogContent>
          <Typography variant="body">{confirmDialog.message}</Typography>
          <ConfirmDialogButtons>
            <Button
              variant="ghost"
              onClick={() => setConfirmDialog(prev => ({ ...prev, show: false }))}
            >
              {confirmDialog.cancelText}
            </Button>
            <Button
              variant="primary"
              onClick={confirmDialog.onConfirm}
            >
              {confirmDialog.confirmText}
            </Button>
          </ConfirmDialogButtons>
        </ConfirmDialogContent>
      </Modal>

      {/* 批量操作栏 */}
      <BatchOperationBar show={isSelectMode && selectedIds.size > 0}>
        <BatchInfo>
          <Typography variant="body">已选择 {selectedIds.size} 项</Typography>
        </BatchInfo>
        <BatchActions>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleBatchToggleStatus(true)}
            disabled={batchOperating}
            loading={batchOperating}
          >
            批量启用
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => handleBatchToggleStatus(false)}
            disabled={batchOperating}
            loading={batchOperating}
          >
            批量禁用
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBatchDelete}
            disabled={batchOperating}
          >
            批量删除
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSelectedIds(new Set());
              setIsSelectMode(false);
            }}
            disabled={batchOperating}
          >
            取消
          </Button>
        </BatchActions>
      </BatchOperationBar>
    </PageContainer>
  );
};

export default AIConfigPage;