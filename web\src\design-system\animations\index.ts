import { Variants } from 'framer-motion';

export const fadeIn: Variants = {
  initial: { opacity: 0, y: 20, transition: { duration: 0.3, ease: 'easeOut' } },
  animate: { opacity: 1, y: 0, transition: { duration: 0.3, ease: 'easeOut' } },
  exit: { opacity: 0, y: -20, transition: { duration: 0.3, ease: 'easeOut' } },
};

export const slideUp: Variants = {
  initial: { y: '100%', transition: { type: 'spring', damping: 25, stiffness: 500 } },
  animate: { y: '0%', transition: { type: 'spring', damping: 25, stiffness: 500 } },
  exit: { y: '100%', transition: { type: 'spring', damping: 25, stiffness: 500 } },
};

export const pageTransition: Variants = {
  initial: { opacity: 0, transition: { duration: 0.3 } },
  animate: { opacity: 1, transition: { duration: 0.3 } },
  exit: { opacity: 0, transition: { duration: 0.3 } },
};

export const loadingSpinner: Variants = {
  animate: { rotate: 360, transition: { duration: 1, repeat: Infinity, ease: 'linear' } },
}; 