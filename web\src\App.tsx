import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import axios from 'axios';
import GlobalStyles from './design-system/styles/GlobalStyles';
import { ThemeProvider } from './design-system/theme/ThemeProvider';
import AppLayout from './layouts/AppLayout';
import AppRoutes from './routes/AppRoutes';
import AuthRoutes from './routes/AuthRoutes';
import ProtectedRoute from './components/common/ProtectedRoute';
import ErrorBoundary from './components/common/ErrorBoundary';

function App() {
  axios.defaults.baseURL = 'http://localhost:3001';

  // 添加请求拦截器，自动携带 token
  axios.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        if (config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }      
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return (
    <ThemeProvider>
      <GlobalStyles />
      <Router>
        <ErrorBoundary>
          <Suspense fallback={<div>載入中...</div>}> {/* 可以替換成更精美的載入組件 */}
            <Routes>
              {/* Authentication Routes */}
              <Route path="/auth/*" element={<AuthRoutes />} />

              {/* Protected Application Routes */}
              <Route
                path="/*"
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <AppRoutes />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
            </Routes>
          </Suspense>
        </ErrorBoundary>
      </Router>
    </ThemeProvider>
  );
}

export default App;
