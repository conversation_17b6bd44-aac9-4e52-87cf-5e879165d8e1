import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from 'design-system/theme/ThemeProvider';
import { PageTransition } from '../components/animations/PageTransition';

interface AuthLayoutProps {
  children: ReactNode;
}

const AuthContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: ${(props) => props.theme.colors.background};
  padding: ${(props) => props.theme.spacing.lg};
`;

export const AuthLayout = ({ children }: AuthLayoutProps) => {
  const { theme } = useTheme();

  return (
    <PageTransition>
      <AuthContainer>{children}</AuthContainer>
    </PageTransition>
  );
}; 