import React from 'react';
import styled from 'styled-components';
import { Button } from '../atoms'; // Assuming Button is in atoms

interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement> | React.KeyboardEvent<HTMLButtonElement>) => void;
  disabled?: boolean;
  ariaLabel?: string;
}

const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  children,
  onClick,
  disabled,
  ariaLabel,
  ...props
}) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick?.(e);
    }
  };

  // This function will be passed to the onClick prop of the underlying styled(Button)
  // It must match the signature expected by <PERSON><PERSON>, which is `() => void`.
  const handleClick = () => {
    // When the base Button is clicked (e.g., by mouse), this wrapper will be called.
    // Since the underlying Button atom's onClick is defined as `() => void`, it doesn't provide a MouseEvent.
    // Therefore, we call the AccessibleButton's onClick prop with `undefined` for the event argument.
    onClick?.(undefined as any); // Cast to any to satisfy linter if onClick strictly requires an argument.
  };

  return (
    <StyledButton
      onClick={onClick ? handleClick : undefined} // Pass the no-argument wrapper function
      onKeyDown={handleKeyDown}
      disabled={disabled}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-label={ariaLabel}
      aria-disabled={disabled}
      {...props}
    >
      {children}
    </StyledButton>
  );
};

const StyledButton = styled(Button)`
  /* Add any specific styling for accessible button if needed, e.g., focus styles */
  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }
`;

export default AccessibleButton; 