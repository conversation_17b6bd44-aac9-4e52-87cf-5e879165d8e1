import React from 'react';
import styled from 'styled-components';

interface ChatLayoutProps {
  children: React.ReactNode;
}

const ChatLayout: React.FC<ChatLayoutProps> = ({ children }) => {
  return <LayoutContainer>{children}</LayoutContainer>;
};

const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh; /* Full viewport height */
  width: 100%;
  max-width: 800px; /* Max width for chat content */
  margin: auto;
  border-left: 1px solid ${({ theme }) => theme.colors.separator};
  border-right: 1px solid ${({ theme }) => theme.colors.separator};
  background-color: ${({ theme }) => theme.colors.background};
`;

export default ChatLayout; 