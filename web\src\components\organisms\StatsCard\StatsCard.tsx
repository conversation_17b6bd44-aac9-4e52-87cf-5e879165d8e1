import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Typography } from '../../atoms';
import { Card } from '../../molecules'; // Assuming Card is in molecules
import { Icon } from '../../atoms'; // Icon is now in atoms

interface StatsCardProps {
  title: string;
  value: number;
  unit: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  unit,
  trend,
  trendValue,
}) => {
  return (
    <StyledCard whileHover={{ scale: 1.02 }}>
      <Typography variant="caption1" color="secondary">
        {title}
      </Typography>
      <ValueContainer>
        <Typography variant="title1">{value}</Typography>
        <Typography variant="caption1">{unit}</Typography>
      </ValueContainer>
      {trend && trendValue !== undefined && (
        <TrendContainer trend={trend}>
          <Icon name={trend === 'up' ? 'arrow-up' : 'arrow-down'} size="16px" />
          <Typography variant="caption1">{trendValue}%</Typography>
        </TrendContainer>
      )}
    </StyledCard>
  );
};

const StyledCard = styled(Card)`
  display: flex;
  flex-direction: column;
  padding: ${({ theme }) => theme.spacing.lg};
  border-radius: 12px;
  min-width: 180px;
  text-align: center;
`;

const ValueContainer = styled.div`
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-top: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const TrendContainer = styled.div<{ trend: 'up' | 'down' | 'stable' }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: ${({ theme, trend }) =>
    trend === 'up'
      ? theme.colors.green[500]
      : trend === 'down'
      ? theme.colors.red[500]
      : theme.colors.textSecondary};

  svg {
    fill: currentColor; /* Ensure icon color matches text */
  }
`;

export default StatsCard; 