import { useState, useEffect } from 'react';

interface Breakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  largeDesktop: number;
}

// Define your breakpoints here
const defaultBreakpoints: Breakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  largeDesktop: 1920,
};

export const useBreakpoint = (breakpoints: Breakpoints = defaultBreakpoints) => {
  const [breakpoint, setBreakpoint] = useState<keyof Breakpoints>('desktop');

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < breakpoints.mobile) {
        setBreakpoint('mobile');
      } else if (width < breakpoints.tablet) {
        setBreakpoint('tablet');
      } else if (width < breakpoints.desktop) {
        setBreakpoint('desktop');
      } else if (width < breakpoints.largeDesktop) {
        setBreakpoint('largeDesktop');
      } else {
        setBreakpoint('largeDesktop'); // For screens larger than largeDesktop
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Set initial breakpoint

    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoints]);

  return breakpoint;
}; 