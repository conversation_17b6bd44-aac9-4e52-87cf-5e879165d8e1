const axios = require('axios');
const { getQuery } = require('../models/db');

// 内存缓存，用于存储AI配置
let aiConfigCache = null;
// 缓存过期时间，单位毫秒 (例如：5分钟)
const CACHE_TTL = 5 * 60 * 1000;

/**
 * @function loadAIConfig
 * @description 从数据库加载当前启用的AI配置，并使用内存缓存。
 * @returns {Promise<Object>} 返回AI配置对象，包含model_id, api_url, api_key_env_var。
 * @throws {Error} 如果没有找到启用的AI配置或数据库查询失败。
 */
async function loadAIConfig() {
  // 检查缓存是否有效
  if (aiConfigCache && (Date.now() - aiConfigCache.timestamp < CACHE_TTL)) {
    console.log('从缓存加载AI配置');
    return aiConfigCache.config;
  }

  console.log('从数据库加载AI配置');
  try {
    // 查询当前启用的AI配置
    const config = await getQuery('SELECT * FROM ai_configs WHERE is_active = 1 LIMIT 1');
    if (!config) {
      throw new Error('未找到启用的AI配置，请在数据库中配置并启用一个AI模型。');
    }

    // 更新缓存
    aiConfigCache = {
      config: {
        model_id: config.model_id,
        api_url: config.api_url,
        api_key_env_var: config.api_key_env_var
      },
      timestamp: Date.now()
    };
    return aiConfigCache.config;
  } catch (err) {
    console.error('加载AI配置失败:', err.message);
    throw new Error(`加载AI配置失败: ${err.message}`);
  }
}

/**
 * @function generateAIResponse
 * @description 使用动态获取的AI配置生成AI响应。
 * @param {string} prompt - 系统提示。
 * @param {string} input - 用户输入。
 * @returns {Promise<string>} 返回AI生成的文本内容。
 * @throws {Error} 如果AI生成失败。
 */
async function generateAIResponse(prompt, input) {
  let aiConfig;
  try {
    aiConfig = await loadAIConfig();
  } catch (error) {
    throw error; // 重新抛出加载配置的错误
  }

  const { model_id, api_url, api_key_env_var } = aiConfig;
  const API_KEY = process.env[api_key_env_var] || '';

  if (!API_KEY) {
    throw new Error(`未配置AI API Key: ${api_key_env_var}`);
  }

  try {
    const response = await axios.post(
      api_url,
      {
        model: model_id,
        messages: [
          { role: 'system', content: prompt },
          { role: 'user', content: input }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'HTTP-Referer': 'YOUR_SITE_URL', // 替换为你的网站URL
          'X-Title': 'YOUR_SITE_NAME', // 替换为你的网站名称
          'Content-Type': 'application/json'
        }
      }
    );
    console.log('AI API 原始回應:', response.data);
    const aiContent = response.data.choices[0].message.content;
    console.log('提取的 AI 內容:', aiContent);
    return aiContent;
  } catch (err) {
    console.error('調用 AI API 失敗:', err.response ? err.response.data : err.message);
    throw new Error(`AI生成失敗: ${err.response ? JSON.stringify(err.response.data) : err.message}`);
  }
}

module.exports = { generateAIResponse };