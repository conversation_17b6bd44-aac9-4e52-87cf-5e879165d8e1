import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Lazy load auth pages
const Login = lazy(() => import('../pages/auth/Login'));
const Register = lazy(() => import('../pages/auth/Register'));

const AuthRoutes: React.FC = () => {
  return (
    <Suspense fallback={<div>Loading authentication...</div>}> {/* Replace with proper loading spinner */}
      <Routes>
        <Route path="/" element={<Navigate to="/login" replace />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        {/* Add more auth routes here */}
      </Routes>
    </Suspense>
  );
};

export default AuthRoutes; 