import React, { useRef, useEffect } from 'react';
import styled from 'styled-components';
import { VariableSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';

interface MessageListProps {
  children: React.ReactNode;
}

const MessageList: React.FC<MessageListProps> = ({ children }) => {
  const listRef = useRef<List>(null);
  const itemRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // This effect will scroll to the bottom when new messages are added
  useEffect(() => {
    if (listRef.current) {
      listRef.current.scrollToItem(React.Children.count(children) - 1, "end");
    }
  }, [children]);

  const getItemSize = (index: number) => {
    // Placeholder for dynamic item sizing. In a real app, you'd calculate this based on content.
    // For now, we'll use a rough estimate or a predefined size.
    const child = React.Children.toArray(children)[index];
    // You might need to measure the actual height of the MessageBubble here
    // For demonstration, let's assume a minimum height or a constant height.
    // A more robust solution would involve measuring rendered elements.
    return 80; // Approximate average height, will need refinement
  };

  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style} ref={(el) => (itemRefs.current[index] = el)}>
      {React.Children.toArray(children)[index]}
    </div>
  );

  return (
    <ListContainer>
      <AutoSizer>
        {({ height, width }: { height: number; width: number }) => (
          <List
            ref={listRef}
            height={height}
            width={width}
            itemCount={React.Children.count(children)}
            itemSize={getItemSize}
            overscanCount={5}
          >
            {Row}
          </List>
        )}
      </AutoSizer>
    </ListContainer>
  );
};

const ListContainer = styled.div`
  flex-grow: 1;
  overflow: hidden; /* Important for AutoSizer and List to work correctly */
  padding: ${({ theme }) => theme.spacing.md};
`;

export default MessageList; 