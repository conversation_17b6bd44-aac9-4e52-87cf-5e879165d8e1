import React, { useState } from 'react';
import styled from 'styled-components';
import { Input, Button, Typography, Icon } from '../../atoms';

interface ChatInputProps {
  onSend: (content: string) => void;
  disabled: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({ onSend, disabled }) => {
  const [message, setMessage] = useState('');

  const handleSend = () => {
    if (message.trim()) {
      onSend(message);
      setMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <InputContainer>
      <TextArea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="輸入訊息..."
        disabled={disabled}
        rows={1}
      />
      <SendButton
        onClick={handleSend}
        disabled={disabled || !message.trim()}
      >
        <Icon name="send" />
      </SendButton>
    </InputContainer>
  );
};

const InputContainer = styled.div`
  display: flex;
  align-items: flex-end;
  padding: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.separator};
  background-color: ${({ theme }) => theme.colors.background};
`;

const TextArea = styled(Input).attrs({ as: 'textarea' })`
  flex-grow: 1;
  resize: none;
  min-height: 40px;
  max-height: 150px; /* Limit height to prevent excessive expansion */
  margin-right: ${({ theme }) => theme.spacing.sm};
  padding: ${({ theme }) => theme.spacing.sm};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.gray[300]};
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`;

const SendButton = styled(Button)`
  height: 40px;
  width: 40px;
  min-width: 40px; /* Ensure button doesn't shrink */
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  svg {
    fill: white; /* Assuming icon needs to be white */
    width: 20px;
    height: 20px;
  }
`;

export default ChatInput; 