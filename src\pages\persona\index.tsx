import React, { useEffect, useState } from 'react';
import axios from 'axios';
import PromptTest from './PromptTest';

type Persona = {
  id: number;
  name: string;
  tags: string[];
  avatar: string;
  prompt: string;
};

const PersonaManager: React.FC = () => {
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [current, setCurrent] = useState<number | null>(null);
  const [form, setForm] = useState<Partial<Persona>>({});
  const [editId, setEditId] = useState<number | null>(null);
  const [error, setError] = useState('');

  const fetchPersonas = async () => {
    const res = await axios.get('/api/persona');
    setPersonas(res.data);
    const cur = await axios.get('/api/persona/current');
    setCurrent(cur.data?.id || null);
  };

  useEffect(() => { fetchPersonas(); }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    try {
      if (editId) {
        await axios.put(`/api/persona/${editId}`, form);
      } else {
        await axios.post('/api/persona', form);
      }
      setForm({});
      setEditId(null);
      fetchPersonas();
    } catch (err: any) {
      setError(err.response?.data?.message || '操作失敗');
    }
  };

  const handleEdit = (p: Persona) => {
    setForm(p);
    setEditId(p.id);
  };

  const handleDelete = async (id: number) => {
    await axios.delete(`/api/persona/${id}`);
    fetchPersonas();
  };

  const handleSwitch = async (id: number) => {
    await axios.post(`/api/persona/switch/${id}`);
    setCurrent(id);
  };

  return (
    <div style={{ maxWidth: 600, margin: 'auto', padding: 32 }}>
      <h2>人格管理</h2>
      <form onSubmit={handleSubmit}>
        <input name="name" placeholder="名稱" value={form.name || ''} onChange={handleChange} required />
        <input name="tags" placeholder="標籤（逗號分隔）" value={form.tags?.join(',') || ''} onChange={e => setForm({ ...form, tags: e.target.value.split(',') })} />
        <input name="avatar" placeholder="頭像URL" value={form.avatar || ''} onChange={handleChange} />
        <textarea name="prompt" placeholder="Prompt" value={form.prompt || ''} onChange={handleChange} />
        <button type="submit">{editId ? '保存編輯' : '創建人格'}</button>
        {editId && <button type="button" onClick={() => { setEditId(null); setForm({}); }}>取消</button>}
      </form>
      {error && <div style={{ color: 'red' }}>{error}</div>}
      <ul>
        {personas.map(p => (
          <li key={p.id} style={{ margin: 8, border: '1px solid #ccc', padding: 8, background: current === p.id ? '#e0f7fa' : '#fff' }}>
            <div><b>{p.name}</b> {p.tags && p.tags.length > 0 && <span>({p.tags.join(',')})</span>}</div>
            {p.avatar && <img src={p.avatar} alt="avatar" style={{ width: 40, height: 40, borderRadius: 20 }} />}
            <div style={{ fontSize: 12, color: '#888' }}>{p.prompt}</div>
            <button onClick={() => handleEdit(p)}>編輯</button>
            <button onClick={() => handleDelete(p.id)}>刪除</button>
            <button onClick={() => handleSwitch(p.id)} disabled={current === p.id}>切換</button>
            {current === p.id && <PromptTest prompt={p.prompt} />}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default PersonaManager; 