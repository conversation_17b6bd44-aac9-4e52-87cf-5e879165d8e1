import React from 'react';
import { Navigate } from 'react-router-dom';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

// This is a placeholder for actual authentication logic.
// In a real application, you would check if the user is authenticated
// (e.g., from a global state, context, or auth token in localStorage).
const isAuthenticated = () => {
  // For demonstration, let's assume a simple check
  const token = localStorage.getItem('token');
  return !!token; 
};

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  if (!isAuthenticated()) {
    // Redirect to login page if not authenticated
    return <Navigate to="/auth/login" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute; 