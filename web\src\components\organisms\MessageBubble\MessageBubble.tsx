import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Typography } from '../../atoms';

interface MessageBubbleProps {
  message: {
    id: string;
    content: string;
    sender: 'user' | 'ai';
    timestamp: Date;
    persona?: string;
  };
  isAnimating?: boolean;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isAnimating }) => {
  const isUser = message.sender === 'user';

  return (
    <BubbleContainer isUser={isUser}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {!isUser && message.persona && (
          <PersonaLabel>{message.persona}</PersonaLabel>
        )}
        <BubbleContent isUser={isUser}>
          <Typography variant="body">{message.content}</Typography>
        </BubbleContent>
        <Timestamp isUser={isUser}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Timestamp>
      </motion.div>
    </BubbleContainer>
  );
};

const BubbleContainer = styled.div<{ isUser: boolean }>`
  display: flex;
  justify-content: ${({ isUser }) => (isUser ? 'flex-end' : 'flex-start')};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const BubbleContent = styled.div<{ isUser: boolean }>`
  background-color: ${({ theme, isUser }) =>
    isUser ? theme.colors.primary : theme.colors.gray[200]};
  color: ${({ theme, isUser }) => (isUser ? '#FFFFFF' : theme.colors.text)};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  border-radius: 8px;
  max-width: 70%;
  word-wrap: break-word;
`;

const PersonaLabel = styled(Typography).attrs({ variant: 'caption1' })`
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
  text-align: left;
`;

const Timestamp = styled(Typography).attrs({ variant: 'caption1' })<{ isUser: boolean }>`
  color: ${({ theme }) => theme.colors.gray[500]};
  margin-top: ${({ theme }) => theme.spacing.xs};
  text-align: ${({ isUser }) => (isUser ? 'right' : 'left')};
`;

export default MessageBubble; 