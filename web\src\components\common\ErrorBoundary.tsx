import React, { Component, ErrorInfo, ReactNode } from 'react';
import styled from 'styled-components';
import { Typography } from '../atoms';

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  public state: ErrorBoundaryState = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI.
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo);
    // You can also log error messages to an error reporting service here
  }

  public render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <FallbackContainer>
          <Typography variant="title1">糟糕！發生了一些錯誤。</Typography>
          <Typography variant="body">我們正在努力修復此問題。</Typography>
          {this.state.error && (
            <ErrorDetails>
              <Typography variant="caption1">錯誤詳情:</Typography>
              <pre>{this.state.error.message}</pre>
            </ErrorDetails>
          )}
        </FallbackContainer>
      );
    }

    return this.props.children;
  }
}

const FallbackContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  background-color: ${({ theme }) => theme.colors.background};
  color: ${({ theme }) => theme.colors.text};
`;

const ErrorDetails = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.gray[100]};
  border-radius: 8px;
  max-width: 600px;
  overflow-x: auto;

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.colors.red[700]};
  }
`;

export default ErrorBoundary; 