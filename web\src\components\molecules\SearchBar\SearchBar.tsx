import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../design-system/theme/ThemeProvider';
import { Input, Icon } from '../../atoms'; // Assuming Input and Icon are exported from atoms/index.ts

interface SearchBarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
}

const SearchBarContainer = styled.div`
  display: flex;
  align-items: center;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.radius.md};
  padding: ${(props) => props.theme.spacing.xs};
  border: 1px solid ${(props) => props.theme.colors.separator};
`;

const StyledInput = styled(Input)`
  flex-grow: 1;
  border: none;
  background-color: transparent;
`;

const ClearButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 ${(props) => props.theme.spacing.xs};
  color: ${(props) => props.theme.colors.textSecondary};

  &:hover {
    color: ${(props) => props.theme.colors.text};
  }
`;

export const SearchBar = ({
  placeholder = 'Search',
  onSearch,
  ...props
}: SearchBarProps) => {
  const { theme } = useTheme();
  const [query, setQuery] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleClear = () => {
    setQuery('');
    if (onSearch) {
      onSearch('');
    }
  };

  const handleSearch = () => {
    if (onSearch) {
      onSearch(query);
    }
  };

  return (
    <SearchBarContainer {...props}>
      <Icon name="search" size="small" color={theme.colors.textSecondary} /> {/* Assuming a 'search' icon exists */}
      <StyledInput
        inputPrefix={null} // Explicitly set to null to avoid type issues from previous Input fixes
        inputSuffix={null} // Explicitly set to null to avoid type issues from previous Input fixes
        type="text"
        placeholder={placeholder}
        value={query}
        onChange={handleInputChange}
        onKeyPress={(e: React.KeyboardEvent) => {
          if (e.key === 'Enter') {
            handleSearch();
          }
        }}
      />
      {query && (
        <ClearButton onClick={handleClear}>
          <Icon name="xmark" size="small" color={theme.colors.textSecondary} /> {/* Assuming an 'xmark' icon exists */}
        </ClearButton>
      )}
    </SearchBarContainer>
  );
}; 