const express = require('express');
const router = express.Router();
const { generateAIResponse } = require('../services/aiService'); // 引入 aiService

// 請將你的OpenAI API Key設為環境變量 OPENAI_API_KEY
// const OPENAI_API_KEY = process.env.OPENAI_API_KEY || ''; // 不再需要在這裡定義
// const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions'; // 不再需要在這裡定義

// POST /api/ai/generate
// body: { prompt: string, input: string }
router.post('/generate', async (req, res) => {
  const { prompt, input } = req.body;
  if (!prompt || !input) return res.status(400).json({ message: 'Prompt和輸入內容必填' });
  // if (!OPENAI_API_KEY) return res.status(500).json({ message: '未配置OpenAI API Key' }); // 邏輯已移至 aiService
  try {
    const result = await generateAIResponse(prompt, input); // 調用 aiService 中的函數
    res.json({ result });
  } catch (err) {
    res.status(500).json({ message: 'AI生成失敗', detail: err.message });
  }
});

module.exports = router; 