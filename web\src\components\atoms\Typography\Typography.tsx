import React, { ReactNode, CSSProperties } from 'react';
import styled from 'styled-components';
import { Typography as TypographyType } from '../../../design-system/tokens/typography';

type TextVariant = keyof TypographyType;

interface TypographyProps {
  variant?: TextVariant;
  color?: string;
  children: ReactNode;
  style?: CSSProperties;
}

const StyledText = styled.p<TypographyProps>`
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: ${(props) => props.color || props.theme.colors.text};

  ${(props) => {
    const typographyStyle = props.theme.typography[props.variant || 'body'];
    return `
      font-size: ${typographyStyle.fontSize};
      font-weight: ${typographyStyle.fontWeight};
      line-height: ${typographyStyle.lineHeight};
      letter-spacing: ${typographyStyle.letterSpacing};
    `;
  }}
`;

export const Typography = ({
  variant = 'body',
  color,
  children,
  style,
  ...props
}: TypographyProps) => {
  return (
    <StyledText variant={variant} color={color} style={style} {...props}>
      {children}
    </StyledText>
  );
};