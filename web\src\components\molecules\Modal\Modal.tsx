import React, { ReactNode, useEffect, useRef, useCallback } from 'react';
import styled, { keyframes } from 'styled-components';
import { Typography } from '../../atoms';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// 动画定义
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
`;

const Overlay = styled.div<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: ${fadeIn} 0.2s ease-out;
  backdrop-filter: blur(2px);
`;

const ModalContent = styled.div<{ size: ModalProps['size'] }>`
  position: relative;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.radius.lg};
  padding: ${(props) => props.theme.spacing.xl};
  box-shadow: ${(props) => props.theme.shadows.lg};
  max-height: 90vh;
  overflow: auto;
  animation: ${slideIn} 0.3s ease-out;

  ${({ size }) => {
    switch (size) {
      case 'sm':
        return 'width: 400px; max-width: 90vw;';
      case 'lg':
        return 'width: 800px; max-width: 90vw;';
      case 'xl':
        return 'width: 1000px; max-width: 95vw;';
      default: // md
        return 'width: 600px; max-width: 90vw;';
    }
  }}

  &:focus {
    outline: none;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.separator};
`;

const Title = styled(Typography).attrs({ variant: 'headline' })`
  color: ${({ theme }) => theme.colors.text};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: ${({ theme }) => theme.radius.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.separator};
    color: ${({ theme }) => theme.colors.text};
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }
`;

const ModalBody = styled.div`
  /* 模态框内容区域 */
`;

export const Modal = ({ isOpen, onClose, children, title, size = 'md' }: ModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // 焦点陷阱逻辑
  const trapFocus = useCallback((e: KeyboardEvent) => {
    if (!modalRef.current) return;

    const focusableElements = modalRef.current.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    if (e.key === 'Tab') {
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    }
  }, []);

  // 键盘事件处理
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      onClose();
    } else {
      trapFocus(event);
    }
  }, [onClose, trapFocus]);

  // 管理焦点和滚动
  useEffect(() => {
    if (isOpen) {
      // 保存当前焦点元素
      previousActiveElement.current = document.activeElement as HTMLElement;

      // 禁用背景滚动
      document.body.style.overflow = 'hidden';

      // 添加键盘事件监听
      document.addEventListener('keydown', handleKeyDown);

      // 设置初始焦点
      setTimeout(() => {
        const firstFocusable = modalRef.current?.querySelector(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        ) as HTMLElement;
        firstFocusable?.focus();
      }, 100);
    }

    return () => {
      if (isOpen) {
        // 恢复背景滚动
        document.body.style.overflow = '';

        // 移除键盘事件监听
        document.removeEventListener('keydown', handleKeyDown);

        // 恢复之前的焦点
        previousActiveElement.current?.focus();
      }
    };
  }, [isOpen, handleKeyDown]);

  if (!isOpen) {
    return null;
  }

  return (
    <Overlay
      isOpen={isOpen}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? "modal-title" : undefined}
    >
      <ModalContent
        ref={modalRef}
        size={size}
        onClick={(e) => e.stopPropagation()}
        tabIndex={-1}
      >
        {title && (
          <ModalHeader>
            <Title>
              <span id="modal-title">{title}</span>
            </Title>
            <CloseButton
              onClick={onClose}
              aria-label="关闭对话框"
              type="button"
            >
              ✕
            </CloseButton>
          </ModalHeader>
        )}
        <ModalBody>
          {children}
        </ModalBody>
      </ModalContent>
    </Overlay>
  );
};