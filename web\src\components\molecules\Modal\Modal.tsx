import React, { ReactNode, useEffect, useRef, useCallback, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { Typography } from '../../atoms';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// 动画定义
const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const fadeOut = keyframes`
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
`;

const slideIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
`;

const slideOut = keyframes`
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
`;

const Overlay = styled.div<{ isOpen: boolean; isClosing?: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: ${({ isClosing }) => isClosing ? fadeOut : fadeIn} 0.3s ease-out;
  backdrop-filter: blur(2px);
`;

const ModalContent = styled.div<{ size: ModalProps['size']; isClosing?: boolean }>`
  position: relative;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.radius.lg};
  padding: ${(props) => props.theme.spacing.xl};
  box-shadow: ${(props) => props.theme.shadows.lg};
  max-height: 90vh;
  overflow: auto;
  animation: ${({ isClosing }) => isClosing ? slideOut : slideIn} 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

  ${({ size }) => {
    switch (size) {
      case 'sm':
        return 'width: 400px; max-width: 90vw;';
      case 'lg':
        return 'width: 800px; max-width: 90vw;';
      case 'xl':
        return 'width: 1000px; max-width: 95vw;';
      default: // md
        return 'width: 600px; max-width: 90vw;';
    }
  }}

  &:focus {
    outline: none;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding-bottom: ${({ theme }) => theme.spacing.md};
  border-bottom: 1px solid ${({ theme }) => theme.colors.separator};
`;

const Title = styled(Typography).attrs({ variant: 'headline' })`
  color: ${({ theme }) => theme.colors.text};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: ${({ theme }) => theme.radius.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.separator};
    color: ${({ theme }) => theme.colors.text};
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }
`;

const ModalBody = styled.div`
  /* 模态框内容区域 */
`;

export const Modal = ({ isOpen, onClose, children, title, size = 'md' }: ModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);
  const [isClosing, setIsClosing] = useState(false);

  // 焦点陷阱逻辑 - 只处理Tab键
  const trapFocus = useCallback((e: KeyboardEvent) => {
    if (!modalRef.current || e.key !== 'Tab') return;

    const focusableElements = modalRef.current.querySelectorAll(
      'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement?.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement?.focus();
      }
    }
  }, []);

  // 处理关闭动画
  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      onClose();
    }, 300);
  }, [onClose]);

  // 键盘事件处理 - 分离ESC和Tab处理
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      event.preventDefault();
      handleClose();
    }
  }, [handleClose]);

  const handleTabKeyDown = useCallback((event: KeyboardEvent) => {
    trapFocus(event);
  }, [trapFocus]);

  // 管理焦点和滚动
  useEffect(() => {
    if (isOpen && !isClosing) {
      // 保存当前焦点元素
      previousActiveElement.current = document.activeElement as HTMLElement;

      // 禁用背景滚动
      document.body.style.overflow = 'hidden';

      // 添加键盘事件监听
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keydown', handleTabKeyDown);

      // 设置初始焦点到第一个输入框
      setTimeout(() => {
        const firstInput = modalRef.current?.querySelector(
          'input:not([disabled]), textarea:not([disabled])'
        ) as HTMLElement;
        if (firstInput) {
          firstInput.focus();
        } else {
          const firstFocusable = modalRef.current?.querySelector(
            'button:not([disabled]), [href], select:not([disabled]), [tabindex]:not([tabindex="-1"])'
          ) as HTMLElement;
          firstFocusable?.focus();
        }
      }, 150);
    }

    return () => {
      // 恢复背景滚动
      document.body.style.overflow = '';

      // 移除键盘事件监听
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keydown', handleTabKeyDown);

      // 恢复之前的焦点
      if (previousActiveElement.current && !isOpen) {
        previousActiveElement.current.focus();
      }
    };
  }, [isOpen, isClosing, handleKeyDown, handleTabKeyDown]);

  if (!isOpen && !isClosing) {
    return null;
  }

  return (
    <Overlay
      isOpen={isOpen}
      isClosing={isClosing}
      onClick={handleClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? "modal-title" : undefined}
    >
      <ModalContent
        ref={modalRef}
        size={size}
        isClosing={isClosing}
        onClick={(e) => e.stopPropagation()}
        tabIndex={-1}
      >
        {title && (
          <ModalHeader>
            <Title>
              <span id="modal-title">{title}</span>
            </Title>
            <CloseButton
              onClick={handleClose}
              aria-label="关闭对话框"
              type="button"
            >
              ✕
            </CloseButton>
          </ModalHeader>
        )}
        <ModalBody>
          {children}
        </ModalBody>
      </ModalContent>
    </Overlay>
  );
};