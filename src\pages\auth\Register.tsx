import React, { useState } from 'react';
import axios from 'axios';

const Register: React.FC = () => {
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [confirm, setConfirm] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    if (password !== confirm) {
      setError('兩次密碼不一致');
      return;
    }
    try {
      await axios.post('/api/user/register', { account, password });
      setSuccess('註冊成功，請登錄');
    } catch (err: any) {
      setError(err.response?.data?.message || '註冊失敗');
    }
  };

  return (
    <div style={{ maxWidth: 400, margin: 'auto', padding: 32 }}>
      <h2>用戶註冊</h2>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          placeholder="賬號"
          value={account}
          onChange={e => setAccount(e.target.value)}
          required
        />
        <input
          type="password"
          placeholder="密碼"
          value={password}
          onChange={e => setPassword(e.target.value)}
          required
        />
        <input
          type="password"
          placeholder="確認密碼"
          value={confirm}
          onChange={e => setConfirm(e.target.value)}
          required
        />
        <button type="submit">註冊</button>
      </form>
      {error && <div style={{ color: 'red' }}>{error}</div>}
      {success && <div style={{ color: 'green' }}>{success}</div>}
    </div>
  );
};

export default Register; 