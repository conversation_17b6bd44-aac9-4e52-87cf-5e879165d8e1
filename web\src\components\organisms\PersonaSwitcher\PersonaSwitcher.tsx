import React from 'react';
import styled from 'styled-components';
import { Typo<PERSON>, Button } from '../../atoms';

interface Persona {
  id: string;
  name: string;
  avatar?: string;
}

interface PersonaSwitcherProps {
  personas: Persona[];
  selected: Persona | null;
  onChange: (persona: Persona) => void;
}

const PersonaSwitcher: React.FC<PersonaSwitcherProps> = ({
  personas,
  selected,
  onChange,
}) => {
  return (
    <SwitcherContainer>
      <Typography variant="subhead">選擇人格:</Typography>
      <PersonaList>
        {personas.map((p) => (
          <PersonaButton
            key={p.id}
            onClick={() => onChange(p)}
            isSelected={selected?.id === p.id}
          >
            {p.avatar && <Avatar src={p.avatar} alt={p.name} />}
            <Typography variant="body">{p.name}</Typography>
          </PersonaButton>
        ))}
      </PersonaList>
    </SwitcherContainer>
  );
};

const SwitcherContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.sm};
  border-bottom: 1px solid ${({ theme }) => theme.colors.separator};
`;

const PersonaList = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const PersonaButton = styled(Button)<{ isSelected: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.xs};
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.surface};
  color: ${({ theme, isSelected }) =>
    isSelected ? '#FFFFFF' : theme.colors.text};
  border: 1px solid ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.separator};
  border-radius: 20px;
  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: ${({ theme, isSelected }) =>
      isSelected ? theme.colors.primary : theme.colors.gray[100]};
  }
`;

const Avatar = styled.img`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
`;

export default PersonaSwitcher; 