import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';

interface LazyImageProps {
  src: string;
  alt: string;
  placeholder?: React.ReactNode; // Optional placeholder content
}

const LazyImage: React.FC<LazyImageProps> = ({ src, alt, placeholder }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 } // Image will load when 10% visible
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current);
      }
      observer.disconnect();
    };
  }, []);

  return (
    <ImageWrapper ref={imgRef}>
      {isInView && (
        <StyledImg
          src={src}
          alt={alt}
          onLoad={() => setIsLoaded(true)}
          style={{ opacity: isLoaded ? 1 : 0 }}
        />
      )}
      {!isLoaded && placeholder && <PlaceholderContainer>{placeholder}</PlaceholderContainer>}
      {!isLoaded && !placeholder && <PlaceholderContainer />} {/* Default empty placeholder */}
    </ImageWrapper>
  );
};

const ImageWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: auto; // Or set a fixed height if known
  min-height: 50px; // A minimum height to prevent layout shifts
  background-color: #f0f0f0; // Placeholder background color
  position: relative;
`;

const StyledImg = styled.img`
  width: 100%;
  height: auto;
  display: block;
  transition: opacity 0.3s ease-in-out;
`;

const PlaceholderContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  color: #888;
  font-size: 0.9em;
`;

export default LazyImage; 