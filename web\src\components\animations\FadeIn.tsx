import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { fadeIn } from '../../design-system/animations';

interface FadeInProps {
  children: ReactNode;
}

export const FadeIn = ({ children }: FadeInProps) => {
  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={fadeIn}
    >
      {children}
    </motion.div>
  );
}; 