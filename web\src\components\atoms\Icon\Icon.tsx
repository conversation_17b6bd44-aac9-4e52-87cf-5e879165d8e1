import React from 'react';
import styled, { DefaultTheme } from 'styled-components';
import { useTheme } from '../../../design-system/theme/ThemeProvider';

interface IconProps {
  name: string;
  size?: string;
  color?: string;
}

// Define props interface for StyledIcon
interface StyledIconProps extends React.SVGProps<SVGSVGElement> {
  iconSize: string;
  iconColor: string;
  theme: DefaultTheme;
}

const StyledIcon = styled.svg<StyledIconProps>`
  width: ${(props) => props.iconSize};
  height: ${(props) => props.iconSize};
  fill: ${(props) => props.iconColor};
`;

const getIconPath = (name: string, color: string, size: string) => {
  switch (name) {
    case 'chevron.right':
      return 'M10.187 6L5.32 10.867 6.187 11.733 12 5.867 6.187 0 5.32 0.867z';
    case 'plus':
      return 'M12 7H7V2h-1v5H0v1h6v5h1v-5h5z';
    case 'xmark':
      return 'M12 1.34L10.66 0 6 4.66 1.34 0 0 1.34 4.66 6 0 10.66 1.34 12 6 7.34 10.66 12 12 10.66 7.34 6z';
    case 'gear':
      return 'M8 12c-2.206 0-4-1.794-4-4s1.794-4 4-4 4 1.794 4 4-1.794 4-4 4zm-.99-10.74C5.23 0.36 4.542 0 3.794 0 2.802 0 1.862 0.446 1.134 1.258L.15 2.242 0 2.382v2.704l-.006.185-.85.85-2.242 2.242v1.414l2.242 2.242.85.85.006.185v2.704l.15.14.984.996C1.862 15.554 2.802 16 3.794 16c.748 0 1.436-.36 2.054-.99L7.054 14.86 7.218 14.714l2.704-.006.185-.006.85-.85 2.242-2.242v-1.414l-2.242-2.242-.85-.85-.006-.185v-2.704l-.15-.14-.984-.996C10.138 0.446 9.198 0 8.206 0c-.748 0-1.436.36-2.054.99L5.946 1.14 5.782 1.286zM4.794 10h-2v-4h2zM8.794 10h-2v-4h2z';
    case 'send':
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill={color}
          width={size}
          height={size}
        >
          <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
        </svg>
      );
    default:
      return null; // Return null for unknown icons or handle as needed
  }
};

const Icon: React.FC<IconProps> = ({ name, size = '24px', color = 'currentColor' }) => {
  const { theme } = useTheme();

  const iconSize = (() => {
    switch (size) {
      case 'small':
        return theme.spacing.md;
      case 'medium':
        return theme.spacing.lg;
      case 'large':
        return theme.spacing.xl;
      default:
        return size;
    }
  })();

  const iconColor = color || theme.colors.text;
  const colorString = typeof iconColor === 'string' ? iconColor : theme.colors.text;
  const path = getIconPath(name, colorString, iconSize);

  if (!path) {
    return null; // Or render a placeholder/error icon
  }

  return (
    <StyledIcon
      viewBox="0 0 16 16"
      iconSize={iconSize}
      iconColor={colorString}
      aria-hidden="true"
    >
      {path}
    </StyledIcon>
  );
};

const IconWrapper = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
`;

export { Icon }; 