const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const router = express.Router();

const { runQuery, getQuery } = require('../models/db');
const JWT_SECRET = process.env.JWT_SECRET || 'dev_secret';

// 註冊
router.post('/register', async (req, res) => {
  const { account, password, role } = req.body;
  if (!account || !password) {
    return res.status(400).json({ message: '賬號和密碼必填' });
  }
  try {
    const existingUser = await getQuery('SELECT * FROM users WHERE account = ?', [account]);
    if (existingUser) {
      return res.status(409).json({ message: '賬號已存在' });
    }
    const hash = await bcrypt.hash(password, 10);
    const userRole = role || 'user'; // 默认角色为 'user'
    await runQuery('INSERT INTO users (account, password, role) VALUES (?, ?, ?)', [account, hash, userRole]);
    res.status(201).json({ message: '註冊成功' });
  } catch (err) {
    console.error('Error during registration:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// 登錄
router.post('/login', async (req, res) => {
  const { account, password } = req.body;
  try {
    const user = await getQuery('SELECT * FROM users WHERE account = ?', [account]);
    if (!user) {
      return res.status(401).json({ message: '用戶不存在' });
    }
    const match = await bcrypt.compare(password, user.password);
    if (!match) {
      return res.status(401).json({ message: '密碼錯誤' });
    }
    const token = jwt.sign({ account: user.account, role: user.role }, JWT_SECRET, { expiresIn: '2h' });
    res.json({ token });
  } catch (err) {
    console.error('Error during login:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// JWT驗證中間件
function auth(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) return res.status(401).json({ message: '未授權' });
  try {
    req.user = jwt.verify(token, JWT_SECRET);
    next();
  } catch {
    res.status(401).json({ message: 'Token無效' });
  }
}

// 登出（前端只需刪除token即可）
router.post('/logout', (req, res) => {
  res.json({ message: '已登出' });
});

// 測試受保護接口
router.get('/profile', auth, (req, res) => {
  res.json({ account: req.user.account });
});

module.exports = router;