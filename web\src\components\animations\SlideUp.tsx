import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { slideUp } from '../../design-system/animations';

interface SlideUpProps {
  children: ReactNode;
}

export const SlideUp = ({ children }: SlideUpProps) => {
  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={slideUp}
    >
      {children}
    </motion.div>
  );
}; 