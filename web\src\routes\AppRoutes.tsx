import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import AdminRoute from '../components/common/AdminRoute';

// Lazy load pages for performance optimization
const PersonaManagement = lazy(() => import('../pages/persona'));
const Chat = lazy(() => import('../pages/chat'));
const Archive = lazy(() => import('../pages/archive'));
const AIConfigPage = lazy(() => import('../pages/admin/AIConfigPage'));

const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}> {/* You can replace this with a proper loading spinner */}
      <Routes>
        <Route path="/" element={<Navigate to="/persona" replace />} />
        <Route path="/persona" element={<PersonaManagement />} />
        <Route path="/chat" element={<Chat />} />
        <Route path="/archive" element={<Archive />} />
        <Route
          path="/admin/aiconfig"
          element={
            <AdminRoute>
              <AIConfigPage />
            </AdminRoute>
          }
        />
        {/* Add more app routes here */}
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;