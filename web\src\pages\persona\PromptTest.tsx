import React, { useState } from 'react';
import axios from 'axios';

interface Props {
  prompt: string;
}

const PromptTest: React.FC<Props> = ({ prompt }) => {
  const [input, setInput] = useState('');
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleTest = async () => {
    setLoading(true);
    setError('');
    setResult('');
    try {
      const res = await axios.post('/api/ai/generate', { prompt, input });
      setResult((res.data as { result: string }).result);
    } catch (err: any) {
      setError(err.response?.data?.message || 'AI生成失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ marginTop: 16, border: '1px dashed #aaa', padding: 16 }}>
      <h4>Prompt測試</h4>
      <textarea
        placeholder="輸入測試內容..."
        value={input}
        onChange={e => setInput(e.target.value)}
        style={{ width: '100%', minHeight: 60 }}
      />
      <button onClick={handleTest} disabled={loading || !input}>測試AI回應</button>
      {loading && <div>生成中...</div>}
      {error && <div style={{ color: 'red' }}>{error}</div>}
      {result && <div style={{ marginTop: 8, background: '#f7f7f7', padding: 8 }}>{result}</div>}
    </div>
  );
};

export default PromptTest; 