import { css } from 'styled-components';

interface Breakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  largeDesktop: number;
}

const defaultBreakpoints: Breakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  largeDesktop: 1920,
};

export const media = (Object.keys(defaultBreakpoints) as Array<keyof Breakpoints>).reduce((acc, label) => {
  acc[label] = (...args: Parameters<typeof css>) => css`
    @media (min-width: ${defaultBreakpoints[label] / 16}em) {
      ${css(...args)}
    }
  `;
  return acc;
}, {} as { [key in keyof Breakpoints]: typeof css }); 