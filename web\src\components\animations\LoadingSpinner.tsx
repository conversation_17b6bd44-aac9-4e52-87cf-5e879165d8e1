import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { loadingSpinner } from 'design-system/animations';
import { useTheme } from 'design-system/theme/ThemeProvider';

const SpinnerContainer = styled(motion.div)`
  width: 24px;
  height: 24px;
  border: 2px solid ${(props) => props.theme.colors.separator};
  border-top-color: ${(props) => props.theme.colors.primary};
  border-radius: 50%;
  display: inline-block;
`;

export const LoadingSpinner = () => {
  return <SpinnerContainer variants={loadingSpinner} animate="animate" />;
};
