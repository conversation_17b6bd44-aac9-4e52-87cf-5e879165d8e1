import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../design-system/theme/ThemeProvider';
import { Icon, Typography, Button } from '../components/atoms';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { toggleTheme } = useTheme();
  const { user, logout, isAdmin } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/auth/login');
  };

  return (
    <LayoutContainer>
      <StyledNavigation>
        <NavSection>
          <NavLink to="/persona">
            <Icon name="persona" size="24px" />
            <Typography variant="caption1">人格管理</Typography>
          </NavLink>
          <NavLink to="/chat">
            <Icon name="chat" size="24px" />
            <Typography variant="caption1">對話</Typography>
          </NavLink>
          <NavLink to="/archive">
            <Icon name="archive" size="24px" />
            <Typography variant="caption1">成長檔案</Typography>
          </NavLink>

          {/* 管理員專用菜單 */}
          {isAdmin() && (
            <AdminSection>
              <SectionDivider />
              <SectionTitle>
                <Typography variant="caption2" color="textSecondary">管理員</Typography>
              </SectionTitle>
              <NavLink to="/admin/aiconfig">
                <Icon name="settings" size="24px" />
                <Typography variant="caption1">大模型配置</Typography>
              </NavLink>
            </AdminSection>
          )}
        </NavSection>

        <BottomSection>
          <UserInfo>
            <Typography variant="caption2" color="textSecondary">
              {user?.account} ({user?.role})
            </Typography>
          </UserInfo>

          <ActionButtons>
            <ThemeToggle onClick={toggleTheme}>
              <Icon name="sun" size="20px" />
            </ThemeToggle>
            <LogoutButton onClick={handleLogout}>
              <Icon name="logout" size="20px" />
            </LogoutButton>
          </ActionButtons>
        </BottomSection>
      </StyledNavigation>
      <MainContent>{children}</MainContent>
    </LayoutContainer>
  );
};

const LayoutContainer = styled.div`
  display: flex;
  height: 100vh;
`;

const StyledNavigation = styled.nav`
  flex-shrink: 0;
  width: 240px;
  background-color: ${({ theme }) => theme.colors.surface};
  border-right: 1px solid ${({ theme }) => theme.colors.separator};
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  flex-direction: column;
  height: 100vh;
`;

const NavSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.sm};

  a {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.spacing.sm};
    padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
    border-radius: ${({ theme }) => theme.radius.md};
    color: ${({ theme }) => theme.colors.textSecondary};
    text-decoration: none;
    transition: all 0.2s ease;

    &.active {
      background-color: ${({ theme }) => theme.colors.primary};
      color: ${({ theme }) => theme.colors.background};
    }

    &:hover:not(.active) {
      background-color: ${({ theme }) => theme.colors.separator};
      color: ${({ theme }) => theme.colors.text};
    }
  }
`;

const AdminSection = styled.div`
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

const SectionDivider = styled.hr`
  border: none;
  height: 1px;
  background-color: ${({ theme }) => theme.colors.separator};
  margin: ${({ theme }) => theme.spacing.md} 0;
`;

const SectionTitle = styled.div`
  padding: 0 ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const BottomSection = styled.div`
  margin-top: auto;
  padding-top: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.separator};
`;

const UserInfo = styled.div`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  text-align: center;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  justify-content: center;
`;

const ThemeToggle = styled.button`
  padding: ${({ theme }) => theme.spacing.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.radius.sm};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.separator};
  color: ${({ theme }) => theme.colors.textSecondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.separator};
    color: ${({ theme }) => theme.colors.text};
  }
`;

const LogoutButton = styled.button`
  padding: ${({ theme }) => theme.spacing.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.radius.sm};
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.red};
  color: ${({ theme }) => theme.colors.red};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.red};
    color: ${({ theme }) => theme.colors.background};
  }
`;

const MainContent = styled.main`
  flex-grow: 1;
  overflow-y: auto;
`;

export default AppLayout; 