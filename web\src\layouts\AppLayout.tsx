import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../design-system/theme/ThemeProvider';
import Navigation from '../components/organisms/Navigation/Navigation'; // Will create this next
import { Icon } from '../components/atoms';
import { Typography } from '../components/atoms';
import { NavLink } from 'react-router-dom';

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { toggleTheme } = useTheme();

  return (
    <LayoutContainer>
      <StyledNavigation>
        <NavLink to="/persona">
          <Icon name="persona" size="24px" />
          <Typography variant="caption1">人格管理</Typography>
        </NavLink>
        <NavLink to="/chat">
          <Icon name="chat" size="24px" />
          <Typography variant="caption1">對話</Typography>
        </NavLink>
        <NavLink to="/archive">
          <Icon name="archive" size="24px" />
          <Typography variant="caption1">成長檔案</Typography>
        </NavLink>
        <ThemeToggle onClick={toggleTheme}>
          <Icon name="sun" size="24px" /> {/* Placeholder for theme icon */}
        </ThemeToggle>
      </StyledNavigation>
      <MainContent>{children}</MainContent>
    </LayoutContainer>
  );
};

const LayoutContainer = styled.div`
  display: flex;
  height: 100vh;
`;

const StyledNavigation = styled(Navigation)`
  flex-shrink: 0; /* Prevent navigation from shrinking */
  width: 200px; /* Example fixed width */
  background-color: ${({ theme }) => theme.colors.surface};
  border-right: 1px solid ${({ theme }) => theme.colors.separator};
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};

  a {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.spacing.sm};
    padding: ${({ theme }) => theme.spacing.sm};
    border-radius: 8px;
    color: ${({ theme }) => theme.colors.textSecondary};

    &.active {
      background-color: ${({ theme }) => theme.colors.primary[100]};
      color: ${({ theme }) => theme.colors.primary};
    }

    &:hover {
      background-color: ${({ theme }) => theme.colors.gray[100]};
    }
  }
`;

const ThemeToggle = styled.button`
  margin-top: auto; /* Push to the bottom */
  padding: ${({ theme }) => theme.spacing.sm};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colors.surface};
  &:hover {
    background-color: ${({ theme }) => theme.colors.gray[100]};
  }
`;

const MainContent = styled.main`
  flex-grow: 1;
  overflow-y: auto;
`;

export default AppLayout; 