import React, { lazy } from 'react';

interface LazyComponentOptions {
  fallback?: React.ReactNode;
}

export const lazyLoad = <T extends React.ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options?: LazyComponentOptions
) => {
  const LazyComponent = lazy(factory);

  // Simplified type for Wrapper to resolve linter errors
  const Wrapper: React.FC<any> = (props: any) => (
    <React.Suspense fallback={options?.fallback || null}>
      <LazyComponent {...props} />
    </React.Suspense>
  );

  // Simplified displayName
  Wrapper.displayName = 'LazyLoadComponent';

  return Wrapper;
}; 