import React from 'react';
import styled, { keyframes } from 'styled-components';
import { motion } from 'framer-motion';

const dotPulse = keyframes`
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
`;

const TypingIndicator: React.FC = () => {
  return (
    <IndicatorContainer>
      <Dot delay="0s" />
      <Dot delay="0.2s" />
      <Dot delay="0.4s" />
    </IndicatorContainer>
  );
};

const IndicatorContainer = styled(motion.div).attrs({
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.3 },
})`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: 8px;
  width: fit-content;
  margin-left: 16px; /* Align with AI message bubble */
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const Dot = styled.div<{ delay: string }>`
  width: 8px;
  height: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 50%;
  animation: ${dotPulse} 1.4s infinite ease-in-out;
  animation-delay: ${({ delay }) => delay};
`;

export default TypingIndicator; 