import React, { useEffect, useState } from 'react';
import axios from 'axios';

type Persona = { id: number; name: string; avatar: string };
type Message = { sender: number; content: string; timestamp: number };
type Chat = { id: number; personas: number[]; messages: Message[] };

const ChatPage: React.FC = () => {
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [selected, setSelected] = useState<number[]>([]);
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [input, setInput] = useState('');
  const [sender, setSender] = useState<number | null>(null);

  useEffect(() => {
    axios.get('/api/persona').then(res => setPersonas(res.data));
    axios.get('/api/chat').then(res => setChats(res.data));
  }, []);

  const createChat = async () => {
    if (selected.length === 0) return;
    const res = await axios.post('/api/chat', { personas: selected });
    setChats([...chats, res.data]);
    setCurrentChat(res.data);
    setSender(selected[0]);
  };

  const openChat = async (id: number) => {
    const res = await axios.get(`/api/chat/${id}`);
    setCurrentChat(res.data);
    setSender(res.data.personas[0]);
  };

  const sendMessage = async () => {
    if (!currentChat || !sender || !input) return;
    await axios.post(`/api/chat/${currentChat.id}/message`, { sender, content: input });
    const res = await axios.get(`/api/chat/${currentChat.id}`);
    setCurrentChat(res.data);
    setInput('');
  };

  return (
    <div style={{ maxWidth: 700, margin: 'auto', padding: 32 }}>
      <h2>對話系統</h2>
      <div>
        <h4>選擇參與人格</h4>
        {personas.map(p => (
          <label key={p.id} style={{ marginRight: 8 }}>
            <input
              type="checkbox"
              checked={selected.includes(p.id)}
              onChange={e => setSelected(e.target.checked ? [...selected, p.id] : selected.filter(id => id !== p.id))}
            />
            {p.name}
          </label>
        ))}
        <button onClick={createChat} disabled={selected.length === 0}>創建新對話</button>
      </div>
      <div style={{ marginTop: 24 }}>
        <h4>歷史對話</h4>
        <ul>
          {chats.map(c => (
            <li key={c.id}>
              <button onClick={() => openChat(c.id)}>對話 {c.id}（{c.personas.map(id => personas.find(p => p.id === id)?.name).join('、')}）</button>
            </li>
          ))}
        </ul>
      </div>
      {currentChat && (
        <div style={{ marginTop: 32, border: '1px solid #ccc', padding: 16 }}>
          <h4>圓桌對話（{currentChat.personas.map(id => personas.find(p => p.id === id)?.name).join('、')}）</h4>
          <div style={{ minHeight: 200, background: '#fafafa', padding: 8 }}>
            {currentChat.messages.map((m, i) => {
              const p = personas.find(p => p.id === m.sender);
              return (
                <div key={i} style={{ margin: 8, textAlign: m.sender === sender ? 'right' : 'left' }}>
                  <span style={{ fontWeight: 'bold', color: '#1976d2' }}>{p?.name}</span>: {m.content}
                  <span style={{ fontSize: 10, color: '#888', marginLeft: 8 }}>{new Date(m.timestamp).toLocaleTimeString()}</span>
                </div>
              );
            })}
          </div>
          <div style={{ marginTop: 16 }}>
            <select value={sender || ''} onChange={e => setSender(Number(e.target.value))}>
              {currentChat.personas.map(id => (
                <option key={id} value={id}>{personas.find(p => p.id === id)?.name}</option>
              ))}
            </select>
            <input
              style={{ width: 300, marginLeft: 8 }}
              value={input}
              onChange={e => setInput(e.target.value)}
              placeholder="輸入消息..."
            />
            <button onClick={sendMessage} disabled={!input}>發送</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatPage; 