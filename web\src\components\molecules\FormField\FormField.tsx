import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { Typography } from '../../atoms'; // Assuming Typography is exported from atoms/index.ts
import { useTheme } from '../../../design-system/theme/ThemeProvider';

interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  children: ReactNode;
  htmlFor?: string;
}

const FieldContainer = styled.div`
  margin-bottom: ${(props) => props.theme.spacing.md};
`;

const LabelText = styled.label<{ required?: boolean }>`
  display: block;
  margin-bottom: ${(props) => props.theme.spacing.xs};
  color: ${(props) => props.theme.colors.text};
  font-weight: ${(props) => props.theme.typography.subhead.fontWeight};

  ${(props) =>
    props.required &&
    `
    &:after {
      content: ' *';
      color: ${props.theme.colors.red};
    }
  `}
`;

const ErrorMessageText = styled(Typography)`
  margin-top: ${(props) => props.theme.spacing.xs};
  color: ${(props) => props.theme.colors.red};
  font-size: ${(props) => props.theme.typography.footnote.fontSize};
`;

export const FormField = ({ label, error, required, children, htmlFor }: FormFieldProps) => {
  const { theme } = useTheme();

  return (
    <FieldContainer>
      <LabelText required={required} htmlFor={htmlFor}>
        <Typography variant="subhead">
          {label}
        </Typography>
      </LabelText>
      {children}
      {error && <ErrorMessageText variant="footnote">{error}</ErrorMessageText>}
    </FieldContainer>
  );
}; 