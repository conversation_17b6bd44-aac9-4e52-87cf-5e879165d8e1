import React, { useEffect, useState } from 'react';
import axios from 'axios';

type Message = { sender: number; content: string; timestamp: number };
type Chat = { id: number; personas: number[]; messages: Message[] };
type Highlight = { chatId: number; messageIdx: number };

const ArchivePage: React.FC = () => {
  const [chats, setChats] = useState<Chat[]>([]);
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [report, setReport] = useState<{ totalChats: number; totalMessages: number }>({ totalChats: 0, totalMessages: 0 });
  const [emotion, setEmotion] = useState<{ positive: number; negative: number }>({ positive: 0, negative: 0 });

  useEffect(() => {
    axios.get('/api/archive/chats').then(res => setChats(res.data));
    axios.get('/api/archive/highlights').then(res => setHighlights(res.data));
    axios.get('/api/archive/report').then(res => setReport(res.data));
    axios.get('/api/archive/emotion').then(res => setEmotion(res.data));
  }, []);

  const toggleHighlight = async (chatId: number, messageIdx: number) => {
    await axios.post('/api/archive/highlight', { chatId, messageIdx });
    const res = await axios.get('/api/archive/highlights');
    setHighlights(res.data);
  };

  const isHighlight = (chatId: number, messageIdx: number) =>
    highlights.some(h => h.chatId === chatId && h.messageIdx === messageIdx);

  return (
    <div style={{ maxWidth: 800, margin: 'auto', padding: 32 }}>
      <h2>成長檔案</h2>
      <div style={{ marginBottom: 24 }}>
        <h4>成長報告</h4>
        <div>對話數：{report.totalChats}，消息數：{report.totalMessages}</div>
        <h4>情緒分析</h4>
        <div>
          <span style={{ color: 'green' }}>正面：{emotion.positive}</span>
          <span style={{ color: 'red', marginLeft: 16 }}>負面：{emotion.negative}</span>
        </div>
      </div>
      <div>
        <h4>對話記錄與高光</h4>
        {chats.map(chat => (
          <div key={chat.id} style={{ border: '1px solid #ccc', margin: 12, padding: 12 }}>
            <div>對話ID：{chat.id}</div>
            <ul>
              {chat.messages.map((m, idx) => (
                <li key={idx} style={{ background: isHighlight(chat.id, idx) ? '#fffde7' : '#fff', padding: 4 }}>
                  <span>{m.content}</span>
                  <button style={{ marginLeft: 8 }} onClick={() => toggleHighlight(chat.id, idx)}>
                    {isHighlight(chat.id, idx) ? '取消高光' : '標記高光'}
                  </button>
                  <span style={{ fontSize: 10, color: '#888', marginLeft: 8 }}>{new Date(m.timestamp).toLocaleTimeString()}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ArchivePage; 